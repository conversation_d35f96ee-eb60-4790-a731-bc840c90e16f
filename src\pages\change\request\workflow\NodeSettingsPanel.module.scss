.settingsPanel {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 400px;
  z-index: 10;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}
.contentPanel {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: white;
  padding: var(--mantine-spacing-md);
  width: 100%;
  height: 100%;
}

.header {
  margin-bottom: 16px;
}

.tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tabPanel {
  flex: 1;
  padding-top: 16px;
}

.footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
  gap: 8px;

  .cancelButton,
  .saveButton {
    flex: 1;
  }
}