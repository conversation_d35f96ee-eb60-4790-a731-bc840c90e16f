/**
 * Centralized color constants for status indicators
 * Using Man<PERSON>'s color system variables for consistency
 */

/**
 * Standard color scheme interface for status indicators
 */
export interface StatusColorScheme {
  bg: string;
  color: string;
  borderColor: string;
}

/**
 * Core status color definitions using <PERSON><PERSON>'s color system
 */
export const STATUS_COLORS = {
  // Success/Approval states - Green theme
  APPROVED: {
    bg: 'var(--mantine-color-green-1)',
    color: 'var(--mantine-color-green-9)',
    borderColor: '1px solid var(--mantine-color-green-3)',
  } as StatusColorScheme,

  ACCEPT: {
    bg: 'var(--mantine-color-green-1)',
    color: 'var(--mantine-color-green-9)',
    borderColor: '1px solid var(--mantine-color-green-3)',
  } as StatusColorScheme,

  // Error/Rejection states - Red theme
  REJECTED: {
    bg: 'var(--mantine-color-red-1)',
    color: 'var(--mantine-color-red-9)',
    borderColor: '1px solid var(--mantine-color-red-3)',
  } as StatusColorScheme,

  REJECT: {
    bg: 'var(--mantine-color-red-1)',
    color: 'var(--mantine-color-red-9)',
    borderColor: '1px solid var(--mantine-color-red-3)',
  } as StatusColorScheme,

  // Progress/Active states - Blue theme
  IN_PROGRESS: {
    bg: 'var(--mantine-color-blue-1)',
    color: 'var(--mantine-color-blue-9)',
    borderColor: '1px solid var(--mantine-color-blue-3)',
  } as StatusColorScheme,

  // Workflow-specific states
  SENT_TO_OWNER: {
    bg: 'var(--mantine-color-violet-1)',
    color: 'var(--mantine-color-violet-9)',
    borderColor: '1px solid var(--mantine-color-violet-3)',
  } as StatusColorScheme,

  SENT_TO_APPROVER: {
    bg: 'var(--mantine-color-yellow-1)',
    color: 'var(--mantine-color-yellow-9)',
    borderColor: '1px solid var(--mantine-color-yellow-3)',
  } as StatusColorScheme,

  // Default/Neutral states
  DEFAULT: {
    bg: 'var(--mantine-color-gray-1)',
    color: 'var(--mantine-color-gray-9)',
    borderColor: '1px solid var(--mantine-color-gray-3)',
  } as StatusColorScheme,

  // Special pending state for approver pills
  PENDING_WHITE: {
    bg: 'white',
    color: 'var(--mantine-color-gray-9)',
    borderColor: '1px solid var(--mantine-color-gray-4)',
  } as StatusColorScheme,
} as const;

/**
 * Pre-defined color mappings for Change Request Review statuses
 */
export const CHANGE_REQUEST_REVIEW_STATUS_COLORS: Record<string, StatusColorScheme> = {
  IN_PROGRESS: STATUS_COLORS.IN_PROGRESS,
  SENT_TO_OWNER: STATUS_COLORS.SENT_TO_OWNER,
  SENT_TO_APPROVER: STATUS_COLORS.SENT_TO_APPROVER,
  APPROVED: STATUS_COLORS.APPROVED,
  REJECTED: STATUS_COLORS.REJECTED,
} as const;

/**
 * Utility function to get status color scheme with fallback to default
 * @param status - The status key to look up
 * @returns StatusColorScheme object with bg, color, and borderColor
 */
export const getStatusColors = (status: string): StatusColorScheme => {
  const normalizedStatus = status?.toUpperCase() as keyof typeof STATUS_COLORS;
  return STATUS_COLORS[normalizedStatus] || STATUS_COLORS.DEFAULT;
};
