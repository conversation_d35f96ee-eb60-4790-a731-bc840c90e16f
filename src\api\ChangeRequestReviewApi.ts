import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createResponseSchema } from '@core/schema/Common';
import { RequestConfig } from '@core/api';
import { ChangeRequestReviewSchema, CoordinatorReviewRequest, OwnerReviewRequest, ApproverStatusRequest } from '@core/schema/ChangeRequestReview';

export class ChangeRequestReviewApi {
  static findChangeRequestReview(changeRequestId: number) {
    return createRequest({
      url: BaseURL.changeRequestReview(changeRequestId),
      method: 'GET',
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  static saveOrUpdateForCoordinator(changeRequestId: number, data: CoordinatorReviewRequest) {
    return createRequest({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/coordinator`,
      method: 'POST',
      data,
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  static saveOrUpdateForOwner(changeRequestId: number, data: OwnerReviewRequest, file?: File) {
    const formData = new FormData();
    const jsonBlob = new Blob([JSON.stringify(data)], { type: 'application/json' });
    formData.append('request', jsonBlob);

    if (file) {
      formData.append('file', file);
    }

    return createRequest({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/owner`,
      method: 'POST',
      data: formData,
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  static updateReviewStatus(changeRequestId: number, data: { status: string }) {
    return createRequest({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/status`,
      method: 'PUT',
      data,
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  static updateApproverStatus(changeRequestId: number, data: ApproverStatusRequest) {
    return createRequest({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/approver/status`,
      method: 'PUT',
      data,
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  static downloadReviewDocument(changeRequestId: number): RequestConfig<Blob> {
    return createRequest<Blob>({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/downloads`,
      method: 'GET',
      responseType: 'blob',
    });
  }
}
