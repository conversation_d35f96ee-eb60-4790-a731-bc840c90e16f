import {
  ChangeRequestDocumentOwnerModel,
  ChangeRequestDocumentApproverForm,
  ChangeRequestDocumentActionStatusType,
  ChangeRequestDocumentApprovalModel,
  ChangeRequestDocumentEnums,
} from '@models/ChangeRequestDocumentGroupModel';
import React, { ClipboardEvent } from 'react';
import { Pill } from '@mantine/core';
import classes from '@pages/change/request/document/components/table/CommonTable.module.scss';

const statusBgMap: Record<ChangeRequestDocumentActionStatusType, { bg: string; icon?: React.ReactNode }> = {
  IN_PROGRESS: { bg: 'var(--mantine-color-blue-1)' },
  TO_BE_SENT: { bg: 'var(--mantine-color-blue-1)' },
  SENT_TO_OWNER: { bg: 'var(--mantine-color-violet-1)' },
  SENT_TO_LEADER: { bg: 'var(--mantine-color-orange-1)' },
  APPROVED: { bg: 'var(--mantine-color-green-1)' },
  APPROVED_SUBMITTED: { bg: 'var(--mantine-color-green-1)' },
  REJECTED: { bg: 'var(--mantine-color-red-1)' },
  REJECTED_SUBMITTED: { bg: 'var(--mantine-color-red-1)' },
};
const statusLabelMap: Record<ChangeRequestDocumentActionStatusType, string> = {
  IN_PROGRESS: 'In Progress',
  TO_BE_SENT: 'To Be Sent',
  SENT_TO_OWNER: 'Sent to Owner',
  SENT_TO_LEADER: 'Sent to Leader',
  APPROVED: 'Approved',
  APPROVED_SUBMITTED: 'Approved',
  REJECTED: 'Rejected',
  REJECTED_SUBMITTED: 'Rejected',
};

export const getApproversByLevel = (approvers: ChangeRequestDocumentApproverForm[], documentApproverLevel: number) =>
  approvers?.filter((approver) => approver.documentApproverLevel === documentApproverLevel) ?? [];

export const updateApproversByLevel = (
  approvers: ChangeRequestDocumentApproverForm[],
  documentApproverLevel: number,
  newList: { username: string; displayName: string }[],
) => {
  const others = approvers.filter((a) => a.documentApproverLevel !== documentApproverLevel);
  return [...others, ...newList.map((u) => ({ username: u.username, displayName: u.displayName, documentApproverLevel }))];
};

export const parseClipboardApprovers = (
  e: ClipboardEvent,
  documentApproverLevel: number,
): { username: string; displayName: string; documentApproverLevel: number }[] | null => {
  try {
    const text = e.clipboardData.getData('text');
    const parsed = JSON.parse(text);
    if (Array.isArray(parsed)) {
      return parsed.map((u) => ({
        username: u.username,
        displayName: u.displayName,
        documentApproverLevel,
      }));
    }
  } catch {
    return null;
  }
  return null;
};

export const renderStatusPill = (status?: ChangeRequestDocumentActionStatusType) => {
  if (!status) {
    return null;
  }

  const { bg } = statusBgMap[status] ?? { bg: 'gray' };
  const label = statusLabelMap[status] ?? status;

  return (
    <Pill bg={bg} radius='xl' size='sm'>
      {label}
    </Pill>
  );
};

export const renderUserPill = (user: ChangeRequestDocumentOwnerModel) => {
  const { displayName = '', isActive, username } = user;
  return (
    <Pill className={isActive ? '' : classes.strike} key={username}>
      {displayName}
    </Pill>
  );
};

export const renderUserApprovePill = (approve: ChangeRequestDocumentApprovalModel) => {
  const { status, user } = approve;
  const { displayName = '', isActive, username } = user;
  const color =
    status === ChangeRequestDocumentEnums.ActionStatus.Enum.APPROVED || status === ChangeRequestDocumentEnums.ActionStatus.Enum.APPROVED_SUBMITTED
      ? 'var(--mantine-color-green-1)'
      : status === ChangeRequestDocumentEnums.ActionStatus.Enum.REJECTED || status === ChangeRequestDocumentEnums.ActionStatus.Enum.REJECTED_SUBMITTED
        ? 'var(--mantine-color-red-1)'
        : undefined;

  return (
    <Pill bg={color} className={isActive ? '' : classes.strike} key={username}>
      {displayName}
    </Pill>
  );
};

// toggle helpers
export const toggleArrayItem = <T,>(list: T[], match: (item: T) => boolean, update: (prev: T | null) => T | null): T[] => {
  const idx = list.findIndex(match);
  if (idx !== -1) {
    const next = update(list[idx]);
    if (next === null) {
      return list.filter((_, i) => i !== idx);
    }
    const copy = [...list];
    copy[idx] = next;
    return copy;
  }
  const newItem = update(null);
  return newItem ? [...list, newItem] : list;
};
