import React from 'react';
import { KanbanIconButton, KanbanTable, KanbanText } from 'kanban-design-system';
import { Badge, Checkbox, Flex } from '@mantine/core';

import { IconMessage } from '@tabler/icons-react';
import { useFormContext } from 'react-hook-form';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { statusConfig, WAITING_APPROVAL } from './approval/ChangeRequestApprovalComponent';
import ApprovalUserSelect from './ApprovalUserSelect';
import { ChangeRequestRoleUser } from '@core/schema/ChangeRequestRoleUser';
import { MappedUser } from './ChangeRequestDetailRole';
import styled from '../ChangeRequestDetail.module.scss';
import { useChangeRequestApprovalContext } from './approval/ChangeRequestApprovalContext';
import { FORMAT_HH_MM_DD_MM_YYYY } from '@common/constants/DateTimeConstants';
import dayjs from 'dayjs';

export const RenderCAB: React.FC = () => {
  const {
    cabGroupIdx,
    cabGroupUserIdx,
    changeFlowNodeId,
    control,
    handleCheckboxChange,
    handleOpenApprovalResults,
    mode,
    modeApproval,
    roleIdx,
    workflowIdx,
  } = useChangeRequestApprovalContext();

  const { getValues } = useFormContext<ChangeRequestRoleList>();
  const roleUserPath = `roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}.users.${cabGroupUserIdx}` as const;
  const changeRequestRoleUser = getValues(`${roleUserPath}`) as ChangeRequestRoleUser;
  const changeNodeName = changeRequestRoleUser.changeNodeName || '';
  const changeRequestApproval = changeRequestRoleUser.changeRequestApproval || WAITING_APPROVAL;

  const changeApprovalStatusConfig = statusConfig[changeRequestApproval.overAllStatus];

  return (
    <>
      <KanbanTable
        data={[changeRequestRoleUser]}
        columns={[
          {
            name: 'role',
            title: 'ID',
            width: '5%',
            customRender: (_, row) => (
              <Flex align='center' gap='xs'>
                <Checkbox
                  onChange={(e) => {
                    const selectedUser: MappedUser = {
                      username: row.username,
                      displayUsername: row.displayUsername,
                      usernameActive: row.usernameActive,
                    };
                    handleCheckboxChange(selectedUser, e.currentTarget.checked);
                  }}
                />
                <KanbanText className={styled.roleName}>{cabGroupUserIdx + 1}</KanbanText>
              </Flex>
            ),
          },
          {
            name: 'cab',
            title: 'CAB',
            customRender: () => (
              <ApprovalUserSelect
                workflowIdx={workflowIdx}
                roleIdx={roleIdx}
                control={control}
                mode={mode}
                cabGroupIdx={cabGroupIdx}
                cabGroupUserIdx={cabGroupUserIdx}
                changeFlowNodeId={changeFlowNodeId}
                modeApproval={modeApproval}
              />
            ),
          },
          {
            name: 'node',
            title: 'Node',
            customRender: () => <KanbanText>{changeNodeName}</KanbanText>,
          },
          ...(modeApproval === ChangeRequestApprovalMode.APPROVAL
            ? [
                {
                  name: 'status',
                  title: 'Status',
                  width: '10%',
                  customRender: () => (
                    <Badge
                      color={changeApprovalStatusConfig.color}
                      radius='xs'
                      style={{ textTransform: 'none' }}
                      leftSection={changeApprovalStatusConfig.icon}>
                      {changeApprovalStatusConfig.label}
                    </Badge>
                  ),
                },
                {
                  name: 'createdDate',
                  title: 'Sent On',
                  customRender: (value?: string | null) => (value ? dayjs(value).format(FORMAT_HH_MM_DD_MM_YYYY) : '-'),
                },
                {
                  name: 'lastApprovalDate',
                  title: 'Acted On',
                  customRender: (value?: string | null) => (value ? dayjs(value).format(FORMAT_HH_MM_DD_MM_YYYY) : '-'),
                },
                {
                  name: 'comment',
                  title: 'Comment',
                  width: '10%',
                  customRender: () => (
                    <KanbanIconButton size='sx' variant='subtle' onClick={handleOpenApprovalResults}>
                      <IconMessage size={20} />
                    </KanbanIconButton>
                  ),
                },
              ]
            : []),
        ]}
        pagination={{ enable: false }}
        topBar={{ enable: false }}
        bottomBar={{ enable: false }}
      />
    </>
  );
};
