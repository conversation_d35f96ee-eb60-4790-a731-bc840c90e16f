import React, { useCallback, useEffect, useRef, useState } from 'react';
import { KanbanButton, KanbanSelect, KanbanTabs } from 'kanban-design-system';

import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';
import ChangeRequestDetailRole from './role/ChangeRequestDetailRole';
import ChangeRequestDetailTab from './detail';
import { Box, Flex, Grid } from '@mantine/core';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useLocation, useParams, useSearchParams } from 'react-router-dom';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { WorkflowPage } from './workflow/WorkflowPage';
import { ChangeRequestModel } from '@core/schema/ChangeRequest';
import { ChangeStageType, ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import useFetch from '@core/hooks/useFetch';
import StageProgress from '@pages/change/request/components/StageProgress';
import ErrorPage from '@pages/base/ErrorPage';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { PermissionAction, PermissionActionModule } from '@common/constants/AclPermissionConstants';
import { ChangeStatusApi } from '@api/ChangeStatus';
import useMutate from '@core/hooks/useMutate';
import ChangeRequestDocumentTab from '@pages/change/request/document';
import { ReviewTab } from './review/ReviewTab';
import { ChangeRequestTabRef } from './ChangeRequestTabRef';
import { useDisclosure } from '@mantine/hooks';
import { NextTabConfirmModal } from '@components/utils/ConfirmMessageUtils';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import TodoSidebar from '@pages/change/request/document/components/TodoSidebar';

export const DEFAULT_CHANGE_REQUEST: ChangeRequestModel = {
  id: 0,
  changeTemplateId: 0,
  templateName: '',
  description: '',
  title: '',
  stage: ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING,
};
const ChangeRequestDetailPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('tab');
  const initTab = (actionParam ?? ChangeRquestTabsEnum.DETAIL) as ChangeRquestTabsEnum;
  const [activeTab, setActiveTab] = useState<ChangeRquestTabsEnum>(initTab);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isShowSidebar, setIsShowSidebar] = useState(false);
  const [currentNextTab, setCurrentNextTab] = useState<ChangeRquestTabsEnum>(initTab);

  const [changeTemplateId, setChangeTemplateId] = useState<number>(0);
  const changeRequestId = Number(useParams().id);
  const { search } = useLocation();
  const queryParams = new URLSearchParams(search);
  const isCreateMode = queryParams.get('action') === EntityAction.CREATE;
  const isViewMode = queryParams.get('action') === EntityAction.VIEW;
  const currentUser = useSelector(getCurrentUser).userInfo;
  const [pendingAccordionId, setPendingAccordionId] = useState<string | null>(null);

  const canEditChange = useCheckPermissons([
    { module: PermissionActionModule.CHANGE_SDP, permission: PermissionAction.CHANGE_UPDATE },
    { module: PermissionActionModule.CHANGE_SDP, permission: PermissionAction.CHANGE_DELETE },
  ]);

  const detailQueryConfig = ChangeRequestApi.findWithId(changeRequestId);
  const {
    data: changeRequestResponse,
    error,
    refetch: refetchChangeRequest,
  } = useFetch(detailQueryConfig, {
    enabled: !!changeRequestId,
  });

  const changeRequest = changeRequestResponse?.data || DEFAULT_CHANGE_REQUEST;
  const [currentStage, setCurrentStage] = useState<ChangeStageType>(ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING);
  const [currentChangeStatusId, setCurrentChangeStatusId] = useState<number>(changeRequest.changeStatusId || 0);

  const { data: statusListResponse, refetch: refetchChangeStatus } = useFetch(
    ChangeStatusApi.findAllStatusInSameStageByStatusId(currentChangeStatusId),
    {
      enabled: true,
    },
  );

  const handleRefetchChangeRequestDetail = () => {
    refetchChangeRequest();
  };
  useEffect(() => {
    const newStatusId = changeRequestResponse?.data?.changeStatusId;
    if (newStatusId && newStatusId !== currentChangeStatusId) {
      setCurrentChangeStatusId(newStatusId);
      refetchChangeStatus();
    }
  }, [changeRequestResponse, currentChangeStatusId, refetchChangeStatus]);
  const tabRef = useRef<ChangeRequestTabRef | null>(null);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const { mutate: updateChangeStatus } = useMutate(ChangeRequestApi.processChangeRequestCoordinatorTransition, {
    successNotification: `Process successfully`,
    onSuccess: (response) => {
      if (response.data?.stage) {
        setCurrentStage(response.data.stage);
        setCurrentChangeStatusId(response.data.changeStatusId || 0);
        refetchChangeStatus();
        tabRef.current?.reload?.();
      }
    },
    errorNotification: (data) => ({ message: data.message }),
  });
  const handleConfirm = useCallback(() => {
    setActiveTab(currentNextTab);
    tabRef.current?.saveTab?.();
    closeModal();
  }, [closeModal, currentNextTab]);

  useEffect(() => {
    if (changeRequestResponse?.data?.stage) {
      setCurrentStage(changeRequestResponse.data.stage);
    }
  }, [changeRequestResponse]);

  if (changeRequestId !== 0) {
    if (!changeRequestResponse && !error) {
      return null;
    }
    if (error) {
      return <ErrorPage />;
    }
  }

  const handleTodoClick = (accordionId: string) => {
    setPendingAccordionId(accordionId);
    setActiveTab(ChangeRquestTabsEnum.DOCUMENT);
  };

  return (
    <Flex direction='column' justify='flex-end' w='100%'>
      <HeaderTitleComponent
        title={''}
        rightSection={
          canEditChange && changeRequestId ? (
            <KanbanSelect
              mb={0}
              placeholder='Actions'
              value={String(currentChangeStatusId)}
              disabled={changeRequest.coordinator !== currentUser?.userName}
              data={statusListResponse?.data?.map((it) => ({ value: String(it.id || 'Unknown'), label: it.action ?? '' }))}
              //Why do you need custom render?
              onChange={(e) => {
                if (e) {
                  updateChangeStatus({ changeRequestId, data: { changeStatusId: Number(e) } });
                }
              }}
            />
          ) : (
            <></>
          )
        }
      />
      <Grid w='100%' gutter='md'>
        {/* Sidebar */}
        {isShowSidebar && sidebarOpen && (
          <Grid.Col span={3}>
            <TodoSidebar changeRequestId={changeRequestId} onTodoClick={handleTodoClick} setSidebarOpen={setSidebarOpen} />
          </Grid.Col>
        )}

        <Grid.Col span={isShowSidebar && sidebarOpen ? 9 : 12}>
          {isViewMode && (
            <Box w='100%' mx='auto' px='md' ta='center'>
              <StageProgress currentStage={currentStage} flowStages={changeRequest.flowStages || []} />
            </Box>
          )}
          <NextTabConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleConfirm} tabTitle={tabRef.current?.tab || ''} />
          <Flex justify='space-between' mb='sm' w='100%'>
            {isShowSidebar && !sidebarOpen && (
              <KanbanButton size='xs' onClick={() => setSidebarOpen(true)} mr={2}>
                Show Todos
              </KanbanButton>
            )}

            <Box w='100%'>
              <KanbanTabs
                configs={{
                  value: activeTab,
                  onChange: (value) => {
                    if (value) {
                      if (tabRef.current?.isFormDirty) {
                        setCurrentNextTab(value as ChangeRquestTabsEnum);
                        openModal();
                      } else {
                        setActiveTab(value as ChangeRquestTabsEnum);
                      }
                    }
                  },
                }}
                tabs={{
                  [ChangeRquestTabsEnum.DETAIL]: {
                    content: <ChangeRequestDetailTab changeRequest={changeRequest} setChangeTemplateId={setChangeTemplateId} />,
                    title: 'Change Detail',
                  },
                  [ChangeRquestTabsEnum.DOCUMENT]: {
                    content: <ChangeRequestDocumentTab setIsShowSidebar={setIsShowSidebar} focusAccordionId={pendingAccordionId} />,
                    title: 'Documents',
                    disabled: isCreateMode,
                  },
                  [ChangeRquestTabsEnum.WORKFLOW]: {
                    content: <WorkflowPage />,
                    title: 'Workflow',
                    disabled: isCreateMode,
                  },
                  [ChangeRquestTabsEnum.CHANGE_ROLE]: {
                    content: (
                      <ChangeRequestDetailRole
                        changeTemplateId={changeTemplateId}
                        activeTab={activeTab}
                        changeRequest={changeRequest}
                        ref={tabRef}
                        currentStage={currentStage}
                        onRefetchAll={handleRefetchChangeRequestDetail}
                      />
                    ),
                    title: 'Approval Levels',
                    disabled: isCreateMode,
                  },
                  ...(currentStage === ChangeStageTypeEnum.Enum.REVIEW_CLOSE && {
                    [ChangeRquestTabsEnum.REVIEW]: {
                      content: <ReviewTab changeRequestId={changeRequestId} />,
                      title: 'Review & Close',
                      disabled: isCreateMode,
                    },
                  }),
                }}
              />
            </Box>
          </Flex>
        </Grid.Col>
      </Grid>
    </Flex>
  );
};

export default ChangeRequestDetailPage;
