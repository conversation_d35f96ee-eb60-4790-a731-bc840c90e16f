import { z } from 'zod';

export const JenkinsJobParameterSchema = z.object({
  name: z.string(),
  type: z.string(),
  defaultValue: z.union([z.string(), z.number(), z.boolean()]).nullable().optional(),
  description: z.string().optional(),
});

/**
 * [K-tool gencode][EN] Zod schema for JenkinsJobTree (flat, same style as JenkinsJobParameterSchema)
 * <AUTHOR> gencode
 * @created_date 2025-07-25
 */
export const JenkinsJobTreeSchema = z.object({
  clazz: z.string(),
  name: z.string(),
  url: z.string(),
  parameters: z.array(JenkinsJobParameterSchema).optional(),
});

export type JenkinsJobParameter = z.infer<typeof JenkinsJobParameterSchema>;
export type JenkinsJobTree = z.infer<typeof JenkinsJobTreeSchema>;
