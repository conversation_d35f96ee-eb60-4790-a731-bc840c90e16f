type CheckBoxUtilsProp<T> = {
  items: T[];
  isSelected: (item: T) => boolean;
  onToggle: (item: T, checked: boolean) => void;
};

export function getCheckAllState<T>({ isSelected, items }: Omit<CheckBoxUtilsProp<T>, 'onToggle'>) {
  const allSelected = items.length > 0 && items.every(isSelected);
  const someSelected = items.some(isSelected);
  return { checked: allSelected, indeterminate: someSelected && !allSelected };
}

export function handleCheckAll<T>({ items, onToggle }: CheckBoxUtilsProp<T>, checked: boolean) {
  items.forEach((item) => onToggle(item, checked));
}
