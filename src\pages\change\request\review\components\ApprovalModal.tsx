import React, { useEffect, useRef } from 'react';
import { Modal, Stack, Flex, Text, Alert, Box, Group, ThemeIcon } from '@mantine/core';
import { KanbanButton, KanbanTextarea } from 'kanban-design-system';
import { IconCheck, IconX, IconAlertTriangle } from '@tabler/icons-react';
import {
  ChangeRequestReviewApprovalStatusEnum,
  ChangeRequestReviewApprovalStatusType,
} from '@common/constants/ChangeRequestReviewApprovalStatusConstants';
import reviewClasses from '../ReviewTab.module.scss';
import { COMMENT_MAX_LENGTH } from '@common/constants/ValidationConstant';

interface ApprovalModalProps {
  opened: boolean;
  onClose: () => void;
  approvalAction: ChangeRequestReviewApprovalStatusType | null;
  approvalComment: string;
  onCommentChange: (comment: string) => void;
  onSubmit: () => void;
}

export const ApprovalModal: React.FC<ApprovalModalProps> = ({ approvalAction, approvalComment, onClose, onCommentChange, onSubmit, opened }) => {
  const isAccept = approvalAction === ChangeRequestReviewApprovalStatusEnum.Enum.ACCEPT;

  // ref for textarea
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);

  useEffect(() => {
    if (opened && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [opened]);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      withCloseButton={false}
      size='md'
      centered
      padding='lg'
      title={isAccept ? 'Approve Request' : 'Reject Request'}
      className={reviewClasses.approvalModal}
      data-modal='approval-modal'>
      <Stack gap='lg'>
        {/* Header */}
        <Flex align='center' justify='space-between' className={reviewClasses.modalHeader}>
          <Flex align='center' gap='sm'>
            <ThemeIcon size='lg' radius='xl' variant='light' color={isAccept ? 'green' : 'red'} className={reviewClasses.modalIcon}>
              {isAccept ? <IconCheck size={20} /> : <IconX size={20} />}
            </ThemeIcon>
            <Text size='lg' fw={600} className={reviewClasses.modalTitle}>
              {isAccept ? 'Approve' : 'Reject'}
            </Text>
          </Flex>
          <KanbanButton variant='filled' size='xs' onClick={onClose} p={4} className={reviewClasses.modalCloseButton}>
            <IconX size={16} />
          </KanbanButton>
        </Flex>

        {/* Warning */}
        <Alert icon={<IconAlertTriangle size={16} />} color='yellow' variant='light' className={reviewClasses.approvalAlert}>
          <Text size='sm' c='dark'>
            Confirming means you{' '}
            <Text component='span' fw={600} c={isAccept ? 'green' : 'red'}>
              {isAccept ? 'approve' : 'reject'}
            </Text>{' '}
            all related items: change details, documents, and workflow
          </Text>
        </Alert>

        {/* Comment */}
        <Box className={reviewClasses.commentSection}>
          <Text size='sm' fw={500} mb='xs' className={reviewClasses.commentLabel}>
            * Comment
          </Text>
          <KanbanTextarea
            ref={textareaRef}
            value={approvalComment}
            onChange={(e) => onCommentChange(e.target.value)}
            placeholder='Enter comments'
            required
            autoFocus={opened}
            data-autofocus
            maxLength={COMMENT_MAX_LENGTH}
            minRows={4}
            className={reviewClasses.modalTextarea}
          />
        </Box>

        {/* Actions */}
        <Group justify='flex-end' gap='sm' className={reviewClasses.modalActions}>
          <KanbanButton variant='filled' size='xs' onClick={onClose} className={reviewClasses.cancelButton}>
            Cancel
          </KanbanButton>
          <KanbanButton variant='filled' size='xs' onClick={onSubmit} disabled={!approvalComment.trim()} className={reviewClasses.confirmButton}>
            Confirm
          </KanbanButton>
        </Group>
      </Stack>
    </Modal>
  );
};
