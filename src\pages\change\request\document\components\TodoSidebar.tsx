import React, { useMemo } from 'react';
import { Box, Card, Flex, Paper, Stack, UnstyledButton } from '@mantine/core';
import { KanbanCheckbox, KanbanText, KanbanTitle } from 'kanban-design-system';
import { IconChevronLeft } from '@tabler/icons-react';
import { ChangeRequestDocumentGroupApi } from '@api/ChangeRequestDocumentGroupApi';
import useFetch from '@core/hooks/useFetch';

interface Props {
  changeRequestId: number;
  toggle?: boolean;
  onTodoClick: (accordionId: string) => void;
  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const TodoSidebar: React.FC<Props> = ({ changeRequestId, onTodoClick, setSidebarOpen }) => {
  const { data } = useFetch(ChangeRequestDocumentGroupApi.findTodoSidebar(changeRequestId), {
    enabled: !!changeRequestId,
  });

  const documentGroups = useMemo(() => data?.data ?? [], [data]);

  return (
    <Paper p='md' withBorder shadow='xs' pos='sticky' top={16}>
      <Flex justify='space-between' align='center' mb='sm'>
        <KanbanTitle order={5}>To-do list</KanbanTitle>
        <UnstyledButton title='Hide To-do' onClick={() => setSidebarOpen(false)}>
          <IconChevronLeft size={18} />
        </UnstyledButton>
      </Flex>

      <Card withBorder radius='md' mb='sm'>
        <Box bg='gray.1' px='md' py={4} mb='xs'>
          <KanbanText size='xs' fw={500}>
            Document
          </KanbanText>
        </Box>

        <Stack mt='xs' gap='xs' mah={500} style={{ overflowY: 'auto' }}>
          {documentGroups.map((documentGroup) => (
            <KanbanCheckbox
              key={documentGroup.id}
              label={documentGroup.name}
              radius='lg'
              color='green'
              readOnly
              checked={false}
              onClick={() => onTodoClick(documentGroup.id.toString())}
              styles={{
                label: {
                  cursor: 'pointer',
                },
              }}
            />
          ))}
        </Stack>
      </Card>
    </Paper>
  );
};

export default TodoSidebar;
