import React from 'react';
import { Box, Stack } from '@mantine/core';
import { Controller, UseFormReturn } from 'react-hook-form';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import { ChangeWorkflow, ChangeWorkflowTypeEnum } from '@core/schema/ChangeWorkflowNode';

export interface ApprovalNotificationFormProps {
  form: UseFormReturn<ChangeWorkflow>;
}

export const WorkflowForm = ({ form }: ApprovalNotificationFormProps) => {
  const { control, formState, register } = form;
  return (
    <Stack>
      <form>
        <Box>
          <KanbanInput
            label='Workflow name'
            placeholder='Enter workflow name'
            withAsterisk
            {...register('name')}
            error={formState.errors.name?.message}
          />
        </Box>

        <Box>
          <Controller
            name='type'
            control={control}
            render={({ field }) => (
              <KanbanSelect
                label='Type'
                placeholder='Pick value'
                data={Object.values(ChangeWorkflowTypeEnum).map((enumValue) => ({
                  value: `${enumValue}`,
                  label: `${enumValue}`,
                }))}
                withAsterisk
                {...field}
              />
            )}
          />
        </Box>
      </form>
    </Stack>
  );
};
