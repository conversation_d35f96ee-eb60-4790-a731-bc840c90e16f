import React, { useState, useCallback, useEffect } from 'react';
import { Box } from '@mantine/core';
import { useReactFlow, NodeResizer, Handle, Position } from '@xyflow/react';
import { IconChevronDown, IconChevronRight } from '@tabler/icons-react';
import clsx from 'clsx';
import styles from './CheckListNode.module.scss';
import { KanbanIconButton } from 'kanban-design-system';
import { NodeData } from './CustomNode';

export type CheckListNodeData = NodeData & {
  label: string;
  icon?: string;
  isCollapsed?: boolean;
};

type CheckListNodeProps = {
  id: string;
  data: CheckListNodeData;
  selected?: boolean;
  width?: number;
  height?: number;
};

const CheckListNode = ({ data, height = 200, id, selected, width = 300 }: CheckListNodeProps) => {
  const [collapsed, setCollapsed] = useState<boolean>(data.isCollapsed ?? false);
  const { setEdges, setNodes } = useReactFlow();

  // Hide/show edges when collapsed/expanded
  useEffect(() => {
    setEdges((edges) =>
      edges.map((edge) => {
        if (edge.source === id || edge.target === id) {
          return { ...edge, hidden: !collapsed };
        }
        return edge;
      }),
    );
  }, [collapsed, id, setEdges]);

  // Handle collapse/expand
  const handleToggle = useCallback(() => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);

    // Update node data and hide/show children
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: { ...node.data, isCollapsed: newCollapsed, originalWidth: node.width, originalHeight: node.height },
            width: newCollapsed ? 300 : (node.data as NodeData).originalWidth,
            height: newCollapsed ? 60 : (node.data as NodeData).originalHeight,
          };
        }

        // Hide/show child nodes
        if (node.parentId === id) {
          return { ...node, hidden: newCollapsed };
        }

        return node;
      }),
    );
  }, [collapsed, id, setNodes]);

  // Tách biến styles ra ngoài để tránh inline calculations
  const containerStyle = {
    width: collapsed ? 300 : width,
    height: collapsed ? 150 : height,
  };

  const containerClasses = clsx(styles.container, {
    [styles['container--collapsed']]: collapsed,
    [styles['container--expanded']]: !collapsed,
    [styles['container--selected']]: selected,
  });

  const headerClasses = clsx(styles.header, {
    [styles['header--collapsed']]: collapsed,
    [styles['header--expanded']]: !collapsed,
  });

  const toggleIcon = collapsed ? <IconChevronRight size={16} /> : <IconChevronDown size={16} />;

  return (
    <Box className={styles.wrapper}>
      <Box className={containerClasses} style={containerStyle}>
        {/* NodeResizer - allow resizing in expanded mode */}
        {!collapsed && (
          <NodeResizer
            minWidth={300}
            minHeight={180} // Tăng từ 150 lên 180
            isVisible={selected}
            handleClassName={styles.resizer}
          />
        )}

        {/* Header */}
        <Box className={headerClasses}>
          <KanbanIconButton onClick={handleToggle} variant='outline' size='sm' className={styles.toggleButton}>
            {toggleIcon}
          </KanbanIconButton>

          {data.icon && (
            <Box className={styles.iconWrapper}>
              <img src={data.icon} alt={data.label} className={styles.icon} />
            </Box>
          )}

          <span className={styles.label} title={data.label}>
            {data.label}
          </span>
        </Box>

        {/* Content area for child nodes */}
        {!collapsed && <Box className={styles.content} />}

        {/* Connection handles */}
        <Handle type='target' position={Position.Left} className={styles.handle} isConnectable={false} />
        <Handle type='source' position={Position.Right} className={styles.handle} isConnectable={false} />
      </Box>
    </Box>
  );
};

export default CheckListNode;
