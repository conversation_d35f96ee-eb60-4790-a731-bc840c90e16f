import React, { useEffect } from 'react';
import { Route, RouterProps, Routes } from 'react-router-dom';
import { RoutePropsType, routeConfigs } from 'routes';
import AppShell from '@components/appShell';
import ForbiddenPage from './base/ForbiddenPage';
import ErrorPage from './base/ErrorPage';
import { useAppDispatch, useAppSelector } from '@store';
import { currentUserSlice, getCurrentUser } from '@slices/CurrentUserSlice';
import UserNotFoundPage from './base/UserNotFoundPage';
import Layout from './sideBar/Layout';
import { currentUserPreferenceSlice, getCurrentUserPreference } from '@slices/UserPreferenceSlice';
import { createWebSocketService, getWebSocketService, resetWebSocketService } from '@api/WebSocketService';

function Index() {
  const dispatch = useAppDispatch();
  const userState = useAppSelector(getCurrentUser);
  const userPreferenceState = useAppSelector(getCurrentUserPreference);

  useEffect(() => {
    dispatch(currentUserSlice.actions.fetchData());
    dispatch(currentUserPreferenceSlice.actions.fetchData());
  }, [dispatch]);

  useEffect(() => {
    if (userState.userInfo?.isActive) {
      const websocketService = getWebSocketService();
      if (websocketService && websocketService.isWebSocketConnected()) {
        return;
      }
      createWebSocketService().connect();
    }

    return () => {
      const websocketService = getWebSocketService();
      if (websocketService && websocketService.isWebSocketConnected()) {
        websocketService.disconnect();
        resetWebSocketService();
      }
    };
  }, [userState.userInfo]);

  const generateRoute = (routeConfig: RoutePropsType, index: number) => {
    const { children, ...rest } = routeConfig;
    const routerProps = rest as RouterProps;
    return (
      <Route key={index} {...routerProps}>
        {children &&
          children.map((x, key) => {
            return generateRoute(x, key);
          })}
      </Route>
    );
  };

  if (userState.isFetching) {
    return null;
  }

  if (userPreferenceState.isFetching) {
    return null;
  }

  if (!userState.userInfo?.isActive) {
    return <UserNotFoundPage />;
  }

  return (
    <>
      <AppShell routers={[]}>
        <Routes>
          <Route path='/' element={<Layout />}>
            {routeConfigs.map((x, key) => generateRoute(x, key))}
            <Route path='/403' element={<ForbiddenPage />} />
            <Route path='*' element={<ErrorPage />} />
          </Route>
        </Routes>
      </AppShell>
    </>
  );
}
export default Index;
