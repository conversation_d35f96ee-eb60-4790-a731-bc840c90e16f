/**
 * [K-tool gencode][EN] Schema for Jenkins Job Tree and Job Parameter.
 * <AUTHOR> gencode
 * @created_date 2025-07-25
 */

export interface JenkinsJobParameterModel {
  name: string;
  type: string;
  defaultValue: any;
  description: string;
}

export interface JenkinsJobTreeModel {
  clazz: string;
  name: string;
  url: string;
  parameters?: JenkinsJobParameterModel[];
  children?: JenkinsJobTreeModel[];
}
