import { z } from 'zod';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { DocumentApproverLevelEnum } from '@common/constants/ChangeDocumentConstants';
import { ChangeStageTypeEnum } from '@common/constants/ChageStageType';

export const ChangeRequestDocumentEnums = {
  Role: z.enum(['COORDINATOR', 'OWNER', 'DOCUMENT_APPROVER', 'GROUP_APPROVER', 'GROUP_DOCUMENT_APPROVER']),
  ActionStatus: z.enum([
    'TO_BE_SENT',
    'IN_PROGRESS',
    'SENT_TO_OWNER',
    'SENT_TO_LEADER',
    'APPROVED',
    'REJECTED',
    'APPROVED_SUBMITTED',
    'REJECTED_SUBMITTED',
  ]),
  DocumentType: z.enum(['FILE', 'URL']),
} as const;

export type ChangeRequestDocumentRole = z.infer<typeof ChangeRequestDocumentEnums.Role>;

export type ChangeRequestDocumentActionStatusType = z.infer<typeof ChangeRequestDocumentEnums.ActionStatus>;

export const LimitedActionStatus: ChangeRequestDocumentActionStatusType[] = ['SENT_TO_LEADER', 'APPROVED', 'REJECTED'];
//Get TodoSidebar
export const ChangeRequestTodoSidebarSchema = z.object({
  id: z.number(),
  name: z.string(),
});

// Get
export const ChangeRequestDocumentUserModelSchema = z.object({
  username: z.string().trim().nonempty(INPUT_REQUIRE),
  displayName: z.string().trim().nonempty(INPUT_REQUIRE),
  isActive: z.boolean().nullish(),
});

export const ChangeRequestDocumentApprovalModelSchema = z.object({
  id: z.number(),
  documentApproverLevel: z.number().nullish(),
  user: ChangeRequestDocumentUserModelSchema,
  status: ChangeRequestDocumentEnums.ActionStatus,
});

export type ChangeRequestDocumentApprovalModel = z.infer<typeof ChangeRequestDocumentApprovalModelSchema>;

export const ChangeRequestDocumentCommentSchema = z.object({
  id: z.number(),
  content: z.string().nullish(),
  createdBy: ChangeRequestDocumentUserModelSchema,
  createdDate: z.string().nullish(),
});

export const ChangeRequestDocumentModelSchema = z.object({
  id: z.number(),
  type: ChangeRequestDocumentEnums.DocumentType,
  documentUrl: z.string().nullish(),
  documentName: z.string(),
  approvers: z.array(ChangeRequestDocumentApprovalModelSchema),
  comments: z.array(ChangeRequestDocumentCommentSchema).nullish(),
  status: ChangeRequestDocumentEnums.ActionStatus,
});

export type ChangeRequestDocumentModel = z.infer<typeof ChangeRequestDocumentModelSchema>;

export const ChangeRequestDocumentOwnerModelSchema = ChangeRequestDocumentUserModelSchema.extend({
  status: ChangeRequestDocumentEnums.ActionStatus.nullish(),
  documents: z.array(ChangeRequestDocumentModelSchema).nullish(),
});

export type ChangeRequestDocumentOwnerModel = z.infer<typeof ChangeRequestDocumentOwnerModelSchema>;

export const ChangeRequestDocumentGroupModelSchema = z.object({
  id: z.number(),
  name: z.string().trim().nonempty(INPUT_REQUIRE),
  owners: z.array(ChangeRequestDocumentOwnerModelSchema).nonempty(INPUT_REQUIRE),
  approvers: z.array(ChangeRequestDocumentApprovalModelSchema).nullish(),
});

export type ChangeRequestDocumentGroupModel = z.infer<typeof ChangeRequestDocumentGroupModelSchema>;

export const ChangeRequestDocumentGroupWithRoleModelSchema = z.object({
  roles: z.array(ChangeRequestDocumentEnums.Role).nullish(),
  groups: z.array(ChangeRequestDocumentGroupModelSchema),
  stage: ChangeStageTypeEnum,
});

export type ChangeRequestDocumentGroupWithRoleModel = z.infer<typeof ChangeRequestDocumentGroupWithRoleModelSchema>;

//Form
export const ChangeRequestDocumentApproverFormSchema = z.object({
  username: z.string().trim().nonempty(INPUT_REQUIRE),
  displayName: z.string().nullish(),
  documentApproverLevel: z.number().nullish(),
});

export type ChangeRequestDocumentApproverForm = z.infer<typeof ChangeRequestDocumentApproverFormSchema>;

export const ChangeRequestDocumentFormSchema = z.object({
  tempId: z.string().nullish(),
  id: z.number().nullish(),
  type: ChangeRequestDocumentEnums.DocumentType.nullish(),
  file: z.instanceof(File).nullish(),
  documentUrl: z.string().trim().nonempty(INPUT_REQUIRE).nullish(),
  documentName: z.string().trim().nullish(),
  status: ChangeRequestDocumentEnums.ActionStatus.nullish(),
  approvers: z
    .array(ChangeRequestDocumentApproverFormSchema)
    .min(1, INPUT_REQUIRE)
    .refine((approvers) => approvers.some((a) => a.documentApproverLevel === DocumentApproverLevelEnum.LEVEL_1), {
      message: INPUT_REQUIRE,
    }),
});

export type ChangeRequestDocumentForm = z.infer<typeof ChangeRequestDocumentFormSchema>;

export const ChangeRequestDocumentOwnerFormSchema = z.object({
  username: z.string().trim().nonempty(INPUT_REQUIRE),
  displayName: z.string().nullish(),
  isActive: z.boolean().nullish(),
  status: ChangeRequestDocumentEnums.ActionStatus.nullish(),
  documents: z
    .array(ChangeRequestDocumentFormSchema)
    .transform((val) => (val.length > 0 ? val : undefined))
    .nullish(),
});

export type ChangeRequestDocumentOwnerForm = z.infer<typeof ChangeRequestDocumentOwnerFormSchema>;

export const ChangeRequestDocumentGroupFormSchema = z.object({
  documentGroupId: z.number().nullish(),
  documentGroupName: z.string().trim().nonempty(INPUT_REQUIRE),
  ownerToChange: z.record(z.string(), z.string()).nullish(),
  owners: z.array(ChangeRequestDocumentOwnerFormSchema).min(1, INPUT_REQUIRE),
  cabs: z.array(ChangeRequestDocumentUserModelSchema).min(1, INPUT_REQUIRE).nullish(),
});

export type ChangeRequestDocumentGroupForm = z.infer<typeof ChangeRequestDocumentGroupFormSchema>;

export const ChangeRequestDocumentGroupFormWrapperSchema = z.object({
  items: z.array(ChangeRequestDocumentGroupFormSchema),
});

export type ChangeRequestDocumentGroupFormWrapper = z.infer<typeof ChangeRequestDocumentGroupFormWrapperSchema>;

//Request
export const ChangeRequestDocumentApproversRequestSchema = z.object({
  username: z.string().trim().min(1, INPUT_REQUIRE),
  documentApproverLevel: z.number().nullish(),
});

export const ChangeRequestDocumentRequestSchema = z.object({
  tempId: z.string().nullish(),
  id: z.number().nullish(),
  file: z.instanceof(File).nullish(),
  type: ChangeRequestDocumentEnums.DocumentType.nullish(),
  documentUrl: z.string().nullish(),
  documentName: z.string().trim().nonempty(INPUT_REQUIRE).nullish(),
  approvers: z.array(ChangeRequestDocumentApproversRequestSchema).min(1, INPUT_REQUIRE),
});
export type ChangeRequestDocumentRequest = z.infer<typeof ChangeRequestDocumentRequestSchema>;

export const ChangeRequestDocumentGroupOwnerRequestSchema = z.object({
  id: z.number().nullish(),
  approvalLevelCount: z.number().nullish(),
  documents: z.array(ChangeRequestDocumentRequestSchema).min(1, INPUT_REQUIRE).default([]),
  approvers: z.array(z.string().trim().min(1)).min(1, INPUT_REQUIRE).default([]),
});
export type ChangeRequestDocumentGroupOwnerRequest = z.infer<typeof ChangeRequestDocumentGroupOwnerRequestSchema>;

export const ChangeRequestDocumentGroupCoordinatorRequestSchema = z.object({
  id: z.number().nullish(),
  name: z.string().nullish(),
  owners: z.array(z.string().trim().min(1)).min(1, INPUT_REQUIRE),
  documentGroup: ChangeRequestDocumentGroupOwnerRequestSchema.nullish(),
  ownerToChange: z.record(z.string(), z.string()).nullish(),
});
export type ChangeRequestDocumentGroupCoordinatorRequest = z.infer<typeof ChangeRequestDocumentGroupCoordinatorRequestSchema>;

//Send to owner request
export const ChangeRequestSendToOwnerRequestSchema = z.object({
  referenceId: z.number(),
  usernames: z.array(z.string()).min(1, INPUT_REQUIRE),
});

export type ChangeRequestSendToOwnerRequest = z.infer<typeof ChangeRequestSendToOwnerRequestSchema>;

//Send to approve request
export const ChangeRequestSendToApproverRequestSchema = z.object({
  groupId: z.number(),
  docIds: z.array(z.number()).min(1, INPUT_REQUIRE),
});

export type ChangeRequestSendToApproverRequest = z.infer<typeof ChangeRequestSendToApproverRequestSchema>;

//Approver - Reject
export const ChangeRequestActionTargetSchema = z.object({
  referenceId: z.number(),
  documentApproverLevel: z.number().nullish(),
});

export type ChangeRequestActionTarget = z.infer<typeof ChangeRequestActionTargetSchema>;

export const ChangeRequestDocumentApproverRejectRequestSchema = z.object({
  action: ChangeRequestDocumentEnums.ActionStatus.nullish(),
  role: ChangeRequestDocumentEnums.Role.nullish(),
  targets: z.array(ChangeRequestActionTargetSchema).min(1, INPUT_REQUIRE),
  comment: z.string().trim().min(1, INPUT_REQUIRE),
});

export type ChangeRequestDocumentApproverRejectRequest = z.infer<typeof ChangeRequestDocumentApproverRejectRequestSchema>;
