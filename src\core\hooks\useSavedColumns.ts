import { SavedDisplayColumns } from '@core/schema/Common';
import { ColumnType } from 'kanban-design-system';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { SaveColumnType } from '@common/constants/UserPreferenceType';
import { UserPreference } from '@core/schema/UserPreference';
import { useSelector } from 'react-redux';
import { currentUserPreferenceSlice, getCurrentUserPreference } from '@slices/UserPreferenceSlice';
import { useAppDispatch } from '@store';
import { useDebouncedCallback } from '@mantine/hooks';
import { DebounceTime } from '@common/constants/DebounceTimeConstant';
import { useGetCurrentUser } from './useGetCurrentUser';

export const useSavedColumns = <T extends object>(
  key: string,
  initialColumns: ColumnType<T>[],
  orderColumn?: string[],
  type: number = SaveColumnType.SAVE_LOCAL_STORAGE,
): ColumnType<T>[] => {
  const currentUserPreference = useSelector(getCurrentUserPreference);
  const currentUser = useGetCurrentUser();
  const userId = currentUser.userInfo?.id;
  const dispatch = useAppDispatch();
  const [savedColumnDisplay, setSavedColumnDisplay] = useState<SavedDisplayColumns<T> | undefined>(() => {
    let value: string | null = null;
    if (type === SaveColumnType.SAVE_DATABASE) {
      value = (currentUserPreference.userPreferences?.[key] as UserPreference)?.content ?? null;
    } else {
      value = localStorage.getItem(key);
    }
    if (!value) {
      return undefined;
    }
    return JSON.parse(value);
  });

  const [initialized, setInitialized] = useState(() => !!savedColumnDisplay);
  const [columns, setColumns] = useState<ColumnType<T>[]>(initialColumns);

  const getColumnMap = useCallback(
    () =>
      initialColumns.reduce<Record<string, ColumnType<T>>>((acc, col) => {
        acc[col.name] = col;
        return acc;
      }, {}),
    [initialColumns],
  );
  const columnMap = useRef<Record<string, ColumnType<T>>>(getColumnMap());

  // Update columnMap when initialColumns changes
  useEffect(() => {
    columnMap.current = getColumnMap();
  }, [getColumnMap]);

  // Effect to merge initialColumns with savedColumnDisplay (runs after mount)
  useEffect(() => {
    if (!initialized) {
      let initialDisplayConfig: SavedDisplayColumns<T> = initialColumns.reduce((acc: SavedDisplayColumns<T>, it) => {
        acc[it.name as keyof T] = !it.hidden;
        return acc;
      }, {});
      if (savedColumnDisplay) {
        initialDisplayConfig = { ...initialDisplayConfig, ...savedColumnDisplay };
      }
      setSavedColumnDisplay(initialDisplayConfig);
      setColumns(initialColumns.map((col) => ({ ...col, hidden: !initialDisplayConfig[col.name as keyof T] })));
      setInitialized(true);
    }
  }, [initialColumns, savedColumnDisplay, setSavedColumnDisplay, initialized]);

  // Effect to update column order
  useEffect(() => {
    if (!orderColumn || !initialized) {
      return;
    }

    setSavedColumnDisplay((prev) => {
      if (!prev) {
        return {};
      }
      const updated: SavedDisplayColumns<T> = {};
      orderColumn.forEach((columnName) => {
        if (columnName in prev) {
          updated[columnName as keyof T] = prev[columnName as keyof T];
        } else if (columnName in columnMap.current) {
          updated[columnName as keyof T] = !columnMap.current[columnName].hidden;
        }
      });
      Object.keys(prev).forEach((key) => {
        if (!(key in updated) && key in columnMap.current) {
          updated[key as keyof T] = prev[key as keyof T];
        }
      });
      return updated;
    });
  }, [orderColumn, initialized]);

  const debouncedSavePreference = useDebouncedCallback(() => {
    if (savedColumnDisplay && userId) {
      dispatch(
        currentUserPreferenceSlice.actions.saveUserPreference({
          data: { userId: userId, keyConfig: key, content: JSON.stringify(savedColumnDisplay) },
        }),
      );
    }
  }, DebounceTime.MILLISECOND_300);

  // Effect to save to localStorage/DB when savedColumnDisplay changes
  useEffect(() => {
    if (savedColumnDisplay) {
      if (type === SaveColumnType.SAVE_DATABASE) {
        debouncedSavePreference();
      } else {
        localStorage.setItem(key, JSON.stringify(savedColumnDisplay));
      }
    }
  }, [key, savedColumnDisplay, type, debouncedSavePreference]);

  const res: ColumnType<T>[] = useMemo(() => {
    if (!initialized || !savedColumnDisplay) {
      return columns;
    }

    return Object.keys(savedColumnDisplay)
      .filter((key) => key in columnMap.current)
      .sort((a, b) => {
        if (orderColumn) {
          const indexA = orderColumn.indexOf(a);
          const indexB = orderColumn.indexOf(b);
          if (indexA !== -1 && indexB !== -1) {
            return indexA - indexB;
          }
          if (indexA !== -1) {
            return -1;
          }
          if (indexB !== -1) {
            return 1;
          }
          return 0;
        }
        return 0;
      })
      .map((key) => {
        const keyx = key as keyof T;
        const isDisplay = savedColumnDisplay[keyx];
        return {
          ...columnMap.current[key],
          hidden: !isDisplay,
          onChangeHidden: (val: boolean) => {
            setSavedColumnDisplay((prev) => ({ ...prev, [keyx]: val }));
          },
        } as ColumnType<T>;
      });
  }, [initialized, savedColumnDisplay, columns, orderColumn]);

  return res;
};
