import React, { useMemo, useState, useEffect, useCallback, useRef } from 'react';
import { ComboboxData, Stack, Paper, ScrollArea, Group, Text, Box, Title, Center } from '@mantine/core';
import { CustomNodeData, NodeParameterData } from '@pages/change/request/workflow/reactFlow/CustomNode';
import { KanbanSelect, KanbanInput, KanbanButton, KanbanIconButton } from 'kanban-design-system';
import styles from './WlaParametersTab.module.scss';
import { WlaApi } from '@api/WlaApi';
import useFetch from '@core/hooks/useFetch';
import { DebounceTime } from '@common/constants/DebounceTimeConstant';
import { useDebouncedValue } from '@mantine/hooks';
import { IconTrash, IconRefresh } from '@tabler/icons-react';
import { v4 as uuidv4 } from 'uuid';

type WlaParametersTabProps = {
  customNodeData: CustomNodeData | undefined;
  setCustomNodeData: (data: React.SetStateAction<CustomNodeData | undefined>) => void;
};

type WlaVariableItem = { id: string; key: string; value: string };

enum OperationType {
  LUNCH = 'Lunch',
  LUNCH_WITH_VARIABLES = 'Lunch with variables',
}

const operationOptions: ComboboxData = [
  { value: OperationType.LUNCH, label: OperationType.LUNCH },
  { value: OperationType.LUNCH_WITH_VARIABLES, label: OperationType.LUNCH_WITH_VARIABLES },
];

const dictToArray = (obj?: Record<string, string>): WlaVariableItem[] =>
  obj ? Object.entries(obj).map(([key, value]) => ({ id: uuidv4(), key, value: value ?? '' })) : [];

const arrayToDict = (arr: WlaVariableItem[]): Record<string, string> =>
  arr.reduce<Record<string, string>>((acc, { key, value }) => {
    const k = key.trim();
    if (k) {
      acc[k] = value;
    }
    return acc;
  }, {});

const WlaParametersTab: React.FC<WlaParametersTabProps> = ({ customNodeData, setCustomNodeData }) => {
  // --- Local states
  const [searchTaskTxt, setSearchTaskTxt] = useState('');
  const [searchTaskDebounced] = useDebouncedValue(searchTaskTxt, DebounceTime.MILLISECOND_300);

  const initialOperation = customNodeData?.parameters?.operation || '';
  const initialTaskId = customNodeData?.parameters?.sysId || '';

  const [operation, setOperation] = useState<string>(initialOperation);
  const [taskId, setTaskId] = useState<string>(initialTaskId);
  const [wlaVars, setWlaVars] = useState<WlaVariableItem[]>(() => dictToArray(customNodeData?.parameters?.wlaParams));

  // Flag to ensure auto-load happens only once for a given (operation, task) pair
  const didAutoloadRef = useRef(false);

  // --- Helpers
  const updateNodeParameters = useCallback(
    (patch: Partial<NodeParameterData>) => {
      setCustomNodeData((prev) => {
        if (!prev) {
          return prev;
        }
        return {
          ...prev,
          parameters: {
            ...(prev.parameters || {}),
            ...patch,
          },
        };
      });
    },
    [setCustomNodeData],
  );

  // --- Fetches
  const { data: tasks } = useFetch(WlaApi.getTasks(customNodeData?.changeNodeId || 0, searchTaskDebounced), {
    enabled: !!customNodeData?.changeNodeId && searchTaskDebounced.length > 0,
  });

  const { data: taskDetails, refetch: refetchDetail } = useFetch(WlaApi.getTasksDetals(customNodeData?.changeNodeId || 0, taskId), {
    enabled: !!customNodeData?.changeNodeId && !!taskId && operation === OperationType.LUNCH_WITH_VARIABLES,
  });

  // --- Reset the autoload flag whenever operation or task changes
  useEffect(() => {
    didAutoloadRef.current = false;
  }, [operation, taskId]);

  // --- Auto-load variables from API ONLY ONCE if the node has no saved variables yet
  useEffect(() => {
    const nodeHasVars = !!customNodeData?.parameters?.wlaParams && Object.keys(customNodeData.parameters.wlaParams).length > 0;

    const apiVars: WlaVariableItem[] =
      taskDetails?.data?.variables?.map((v: { name: string; value: string }) => ({
        id: uuidv4(),
        key: v.name,
        value: v.value,
      })) ?? [];

    if (
      operation === OperationType.LUNCH_WITH_VARIABLES &&
      taskId &&
      !didAutoloadRef.current && // not autoloaded yet for the current (operation, task)
      !nodeHasVars && // the node has no persisted variables
      wlaVars.length === 0 && // local state is empty
      apiVars.length > 0 // API returned data
    ) {
      didAutoloadRef.current = true; // mark as autoloaded
      setWlaVars(apiVars);
      updateNodeParameters({ wlaParams: arrayToDict(apiVars) });
    }
  }, [operation, taskId, taskDetails?.data?.variables, customNodeData?.parameters?.wlaParams, wlaVars.length, updateNodeParameters]);

  // --- Refresh: always fetch from API and overwrite local + node state
  const handleRefresh = useCallback(async () => {
    const result = await refetchDetail();
    const apiVars: WlaVariableItem[] =
      result?.data?.data?.variables?.map((v: { name: string; value: string }) => ({
        id: uuidv4(),
        key: v.name,
        value: v.value,
      })) ?? [];

    setWlaVars(apiVars);
    updateNodeParameters({ wlaParams: arrayToDict(apiVars) });

    // After refresh, consider autoload completed so the effect won't trigger again
    didAutoloadRef.current = true;
  }, [refetchDetail, updateNodeParameters]);

  // --- Task options
  const taskOptions: ComboboxData = useMemo(() => {
    if (tasks?.data?.length) {
      return tasks.data.map((t: { sysId: string; name: string }) => ({ value: t.sysId, label: t.name }));
    }
    if (taskId && customNodeData?.parameters?.taskName) {
      return [{ value: taskId, label: customNodeData.parameters.taskName }];
    }
    return [];
  }, [tasks?.data, taskId, customNodeData?.parameters?.taskName]);

  // --- Handlers
  const handleOperationChange = (value: string | null) => {
    const op = value || '';
    setOperation(op);

    // Changing operation => reset variables (no API call), reset autoload flag so the new task can autoload once
    didAutoloadRef.current = false;
    setWlaVars([]);
    updateNodeParameters({
      operation: op,
      jobPath: '',
      jenkinsParams: [],
      jenkinsParamValues: {},
      wlaParams: {},
    });
  };

  const handleTaskChange = (value: string | null) => {
    if (!value) {
      return;
    }

    setTaskId(value);
    const found = tasks?.data?.find((x: { sysId: string }) => x.sysId === value);

    // Changing task => reset variables (no API call). Allow one autoload for the new task.
    didAutoloadRef.current = false;
    setWlaVars([]);
    updateNodeParameters({
      sysId: value,
      taskName: found?.name || customNodeData?.parameters?.taskName || '',
      wlaParams: {},
    });
  };

  const updateVariables = (next: WlaVariableItem[]) => {
    setWlaVars(next);
    updateNodeParameters({ wlaParams: arrayToDict(next) });
  };

  const handleAddVariable = () => updateVariables([...wlaVars, { id: uuidv4(), key: '', value: '' }]);
  const handleRemoveVariable = (id: string) => updateVariables(wlaVars.filter((x) => x.id !== id));
  const handleVariableChange = (id: string, field: 'key' | 'value', val: string) => {
    const next = wlaVars.map((x) => (x.id === id ? { ...x, [field]: val } : x));
    updateVariables(next);
  };

  const isVariablesMode = operation === OperationType.LUNCH_WITH_VARIABLES;

  return (
    <Stack gap='md'>
      <KanbanSelect
        required
        label='Operation'
        placeholder='Select operation'
        data={operationOptions}
        value={operation}
        onChange={handleOperationChange}
        clearable={false}
      />

      {operation && (
        <KanbanSelect
          required
          label='Task'
          placeholder='Search a task'
          data={taskOptions}
          value={taskId}
          searchable
          onSearchChange={setSearchTaskTxt}
          onChange={handleTaskChange}
        />
      )}

      {isVariablesMode && taskId && (
        <Paper className={styles.variablesContainer}>
          <Group justify='space-between' mb='md'>
            <Title order={5}>Variables</Title>
            <KanbanButton size='compact-xs' variant='subtle' leftSection={<IconRefresh size={16} />} onClick={handleRefresh}>
              Refresh
            </KanbanButton>
          </Group>

          <Group className={styles.tableHeader} mb='xs'>
            <Text fw={600} className={styles.colKey}>
              Key
            </Text>
            <Text fw={600} className={styles.colValue}>
              Value
            </Text>
            <Text fw={600} className={styles.colAction}>
              Action
            </Text>
          </Group>

          <ScrollArea h={250} className={styles.scrollArea}>
            {wlaVars.length === 0 ? (
              <Center h={100}>
                <Text c='dimmed'>No variables configured. Click &quot;Add variable&quot; to start.</Text>
              </Center>
            ) : (
              <Stack gap='xs'>
                {wlaVars.map((variable) => (
                  <Group key={variable.id} className={styles.variableRow} gap='sm'>
                    <Box className={styles.colKey}>
                      <KanbanInput
                        placeholder='Enter key'
                        value={variable.key}
                        onChange={(e) => handleVariableChange(variable.id, 'key', e.target.value)}
                        size='sm'
                      />
                    </Box>
                    <Box className={styles.colValue}>
                      <KanbanInput
                        placeholder='Enter value'
                        value={variable.value}
                        onChange={(e) => handleVariableChange(variable.id, 'value', e.target.value)}
                        size='sm'
                      />
                    </Box>
                    <Box className={styles.colAction}>
                      <KanbanIconButton variant='subtle' color='red' onClick={() => handleRemoveVariable(variable.id)}>
                        <IconTrash size={18} />
                      </KanbanIconButton>
                    </Box>
                  </Group>
                ))}
              </Stack>
            )}
          </ScrollArea>

          <Group mt='md'>
            <KanbanButton size='compact-sm' variant='filled' onClick={handleAddVariable}>
              + Add variable
            </KanbanButton>
          </Group>
        </Paper>
      )}
    </Stack>
  );
};

export default WlaParametersTab;
