import { ActionIcon, Anchor, Flex, Group, Table } from '@mantine/core';
import {
  ChangeRequestDocumentActionStatusType,
  ChangeRequestDocumentForm,
  ChangeRequestDocumentGroupFormWrapper,
} from '@models/ChangeRequestDocumentGroupModel';
import React, { useEffect } from 'react';
import classes from './CommonTable.module.scss';
import { useFieldArray, useFormContext, Controller, useWatch } from 'react-hook-form';
import { IconCirclePlus, IconCopy, IconFile, IconLink, IconTrash, IconX } from '@tabler/icons-react';
import UserSelectComponent from '@pages/change/request/document/components/selectBox/UserSelectComponent';
import { LeaderCell } from './LeaderCell';
import { UploaderFilePopover } from './UploaderFilePopover';
import UserSingleSelectComponent from '../selectBox/UserSingleSelectComponent';
import { renderStatusPill } from '@pages/change/request/document/utils/TableUtils';
import { DocumentApproverLevelEnum } from '@common/constants/ChangeDocumentConstants';
import { useClipboard } from '@mantine/hooks';
import { useChangeRequestDocumentPermission } from '@core/hooks/useChangeRequestDocumentPermission';
import { useGetCurrentUser } from '@core/hooks/useGetCurrentUser';
import { normalizeUrl } from '@common/utils/UrlUtils';

const emptyDocument: ChangeRequestDocumentForm = {
  documentName: '',
  documentUrl: undefined,
  tempId: undefined,
  type: undefined,
  file: undefined,
  approvers: [],
};

interface OwnerRowProps {
  ownerIndex: number;
  removeOwner: (indexOwner: number) => void;
  canEditCab: boolean;
  originalUserName: string;
  totalDocumentRows: number;
  itemName: `items.${number}`;
  ownerName: `items.${number}.owners.${number}`;
  isLeaderLevel2Visible: boolean;
  openedPopoverId: string | null;
  ownerLength: number;
  setOpenedPopoverId: (id: string | null) => void;
}

export const OwnerRow = React.memo(function OwnerRow({
  canEditCab,
  isLeaderLevel2Visible,
  itemName,
  openedPopoverId,
  originalUserName,
  ownerIndex,
  ownerLength,
  ownerName,
  removeOwner,
  setOpenedPopoverId,
  totalDocumentRows,
}: OwnerRowProps) {
  const { control, getValues, resetField, setValue } = useFormContext<ChangeRequestDocumentGroupFormWrapper>();
  const owner = useWatch({ control, name: ownerName });
  const { canEditRow, isCoordinator } = useChangeRequestDocumentPermission({ currentOwner: owner });
  const userName = useGetCurrentUser()?.userInfo?.userName ?? '';
  const isFirstOwnerRow = ownerIndex === 0;
  const clipboard = useClipboard();

  const {
    append,
    fields: documentFields,
    insert,
    remove: removeDoc,
  } = useFieldArray({
    control,
    name: `${ownerName}.documents`,
  });

  useEffect(() => {
    if (canEditRow && documentFields.length === 0) {
      setValue(`${itemName}.cabs`, []);
      append({ documentName: '', approvers: [] });
    }
  }, [canEditRow, documentFields.length, append, setValue, itemName]);

  const appendEmptyDocument = () => append({ documentName: '', approvers: [] });
  const appendEmptyDocumentAt = (index: number) => {
    insert(index, { documentName: '', approvers: [] });
  };

  const handleCopyApprovers = (users: { username: string; displayName?: string | null }[]) => {
    if (!users?.length) {
      return;
    }

    const text = JSON.stringify(
      users.map((u) => ({
        username: u.username,
        displayName: u.displayName ?? '',
      })),
    );

    clipboard.copy(text);
  };

  const handleDeleteDocumentRow = (docIndex: number) => {
    if (documentFields.length === 1) {
      removeOwner(ownerIndex);
      if (userName === owner?.username) {
        setValue(`${itemName}.cabs`, null);
      }
    } else {
      removeDoc(docIndex);
    }
  };

  const handleDeleteDocument = (value: ChangeRequestDocumentForm, onChange: (val: ChangeRequestDocumentForm) => void) => {
    setOpenedPopoverId(null);
    onChange({ ...value, tempId: undefined, type: undefined, file: undefined, documentName: '', documentUrl: '' });
  };

  const renderOwnerCell = (rowSpan: number) => (
    <Controller
      control={control}
      name={`${ownerName}.username`}
      render={({ field, fieldState }) => (
        <Table.Td className={classes.cell} style={{ verticalAlign: 'top' }} rowSpan={rowSpan}>
          <UserSingleSelectComponent
            value={{ value: field.value, label: field.value }}
            onChange={(val) => {
              if (isCoordinator && val && field.value !== val && !!originalUserName) {
                const currentMap = getValues(`${itemName}.ownerToChange`) ?? {};
                const { [originalUserName]: _, ...rest } = currentMap;

                setValue(
                  `${itemName}.ownerToChange`,
                  {
                    ...rest,
                    [originalUserName]: val,
                  },
                  { shouldDirty: true, shouldValidate: true },
                );

                resetField(`${ownerName}.status`);
                resetField(`${ownerName}.documents`);
                resetField(`${itemName}.cabs`);
              }
              field.onChange(val || '');
            }}
            error={fieldState.error?.message}
            isChangeCoordinator={isCoordinator}
          />
        </Table.Td>
      )}
    />
  );

  const renderDocumentCell = (docIndex: number, value: ChangeRequestDocumentForm, onChange: (val: ChangeRequestDocumentForm) => void) => (
    <Table.Td className={classes.cell}>
      {value?.documentName ? (
        <Group gap={4}>
          {value.documentUrl ? <IconLink size={14} /> : <IconFile size={14} />}
          <Anchor fw='500' size='sm' href={normalizeUrl(value.documentUrl) ?? undefined} target='_blank' rel='noopener noreferrer'>
            {value.documentName}
          </Anchor>
          {canEditRow && (
            <ActionIcon variant='subtle' onClick={() => handleDeleteDocument(value, onChange)}>
              <IconX size={14} />
            </ActionIcon>
          )}
        </Group>
      ) : (
        <UploaderFilePopover
          disableUpload={!canEditRow}
          opened={openedPopoverId === `${ownerName}.documents.${docIndex}`}
          setOpened={(open) => setOpenedPopoverId(open ? `${ownerName}.documents.${docIndex}` : null)}
          nameDocument={`${ownerName}.documents.${docIndex}`}
        />
      )}
    </Table.Td>
  );

  const renderLeaderCells = (docIndex: number) =>
    [DocumentApproverLevelEnum.LEVEL_1, ...(isLeaderLevel2Visible ? [DocumentApproverLevelEnum.LEVEL_2] : [])].map((level) => (
      <Table.Td key={level} className={classes.cell}>
        <LeaderCell canEditRow={canEditRow} name={`${ownerName}.documents.${docIndex}.approvers`} level={level} onCopy={handleCopyApprovers} />
      </Table.Td>
    ));

  const renderLeaderEmptyCells = () =>
    [DocumentApproverLevelEnum.LEVEL_1, ...(isLeaderLevel2Visible ? [DocumentApproverLevelEnum.LEVEL_2] : [])].map((level) => (
      <Table.Td key={level} className={classes.cell}>
        <Flex gap={4} align='center'>
          <UserSelectComponent disable value={[]} onChange={() => null} />
          <ActionIcon disabled={!canEditRow} variant='subtle' size='sm' color='black' title='Copy'>
            <IconCopy />
          </ActionIcon>
        </Flex>
      </Table.Td>
    ));

  const renderCabCell = (rowSpan: number) =>
    isFirstOwnerRow && (
      <Table.Td rowSpan={rowSpan} className={classes.cell} style={{ verticalAlign: 'top' }}>
        <Controller
          control={control}
          name={`${itemName}.cabs`}
          render={({ field, fieldState }) => (
            <UserSelectComponent
              disable={!canEditCab}
              value={(field.value ?? []).map((u) => ({ id: u.username, name: u.displayName }))}
              onChange={(val) => field.onChange(val.map((v) => ({ username: v.id, displayName: v.name })))}
              error={fieldState.error?.message}
              onBlur={field.onBlur}
              isCAB={true}
            />
          )}
        />
      </Table.Td>
    );

  return documentFields.length > 0 ? (
    documentFields.map((doc, docIndex) => (
      <Table.Tr key={doc.id}>
        {docIndex === 0 && renderOwnerCell(documentFields.length)}
        <Controller name={`${ownerName}.documents.${docIndex}`} render={({ field }) => renderDocumentCell(docIndex, field.value, field.onChange)} />
        {renderLeaderCells(docIndex)}
        {isFirstOwnerRow && docIndex === 0 && renderCabCell(totalDocumentRows)}
        <Table.Td className={classes.cell}>{doc?.status ? renderStatusPill(doc.status) : owner.status && renderStatusPill(owner.status)}</Table.Td>
        <Table.Td className={classes.cell}></Table.Td>
        <Table.Td className={classes.cell}>
          <Flex align='center' gap='md'>
            <ActionIcon title='Add row document' disabled={!canEditRow} variant='subtle' onClick={() => appendEmptyDocumentAt(docIndex + 1)}>
              <IconCirclePlus size={20} />
            </ActionIcon>
            <ActionIcon
              title='Delete row document'
              disabled={!canEditRow || totalDocumentRows === 1}
              variant='subtle'
              color='red'
              onClick={() => handleDeleteDocumentRow(docIndex)}>
              <IconTrash size={20} />
            </ActionIcon>
          </Flex>
        </Table.Td>
      </Table.Tr>
    ))
  ) : (
    <Table.Tr>
      {renderOwnerCell(1)}
      {canEditRow ? (
        <Controller name={`${ownerName}.documents.0`} render={({ field }) => renderDocumentCell(0, field.value, field.onChange)} />
      ) : (
        renderDocumentCell(0, emptyDocument, () => {})
      )}
      {canEditRow ? renderLeaderCells(0) : renderLeaderEmptyCells()}
      {isFirstOwnerRow && renderCabCell(totalDocumentRows)}
      <Table.Td className={classes.cell}>{owner?.status && renderStatusPill(owner.status as ChangeRequestDocumentActionStatusType)}</Table.Td>
      <Table.Td className={classes.cell}></Table.Td>
      <Table.Td className={classes.cell}>
        <Flex align='center' gap='md'>
          <ActionIcon title='Add row document' variant='subtle' disabled={!canEditRow} onClick={appendEmptyDocument}>
            <IconCirclePlus size={20} />
          </ActionIcon>
          <ActionIcon
            title='Delete owner'
            disabled={!isCoordinator || ownerLength === 1}
            color='red'
            variant='subtle'
            onClick={() => removeOwner(ownerIndex)}>
            <IconTrash size={20} />
          </ActionIcon>
        </Flex>
      </Table.Td>
    </Table.Tr>
  );
});
