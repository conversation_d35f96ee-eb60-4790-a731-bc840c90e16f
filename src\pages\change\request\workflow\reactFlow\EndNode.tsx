import { Box, Text } from '@mantine/core';
import { Handle, Position } from '@xyflow/react';
import React from 'react';
import styles from './EndNode.module.scss';
import { NodeData } from './CustomNode';
import clsx from 'clsx';
import { IconPlayerStop } from '@tabler/icons-react';

interface EndNodeProps {
  id: string;
  data: NodeData;
  selected?: boolean;
}

function EndNode({ data, selected }: EndNodeProps) {
  const nodeClass = clsx(styles.endNode, selected && styles.selected, data.isRunning && styles.running);

  const iconClass = clsx(styles.icon, data.isRunning && styles.runningIcon);
  const handleClass = clsx(styles.handle, data.isRunning && styles.runningHandle);

  return (
    <Box className={nodeClass} aria-label={data.label || 'End node'}>
      {/* Header: icon + title */}
      <Box className={styles.header}>
        <Box className={styles.iconWrap} aria-hidden='true' title='End'>
          <span className={iconClass}>
            <IconPlayerStop size={16} stroke={1.8} />
          </span>
        </Box>

        <Text className={styles.title}>{data.label || 'End'}</Text>
      </Box>

      {/* Body: status */}
      <Box className={styles.body}>
        <Text className={styles.subtleHint}>End Wofkflow</Text>
      </Box>

      {/* Ports (React Flow handles) */}
      <Handle type='target' position={Position.Left} className={clsx(handleClass, styles.handleIn)} id='target-left' />
    </Box>
  );
}

export default EndNode;
