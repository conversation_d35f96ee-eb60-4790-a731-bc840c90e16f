import { EntityAction } from '@common/constants/EntityActionConstants';
import { buildChangeDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { ColumnType, KanbanButton, KanbanIconButton, KanbanTable, KanbanTableProps, KanbanText, renderDateTime } from 'kanban-design-system';
import React, { useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import stylesCss from './WorkflowPage.module.scss';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { ChangeWorkflow, ChangeWorkflowTypeEnum } from '@core/schema/ChangeWorkflowNode';
import useFetch from '@core/hooks/useFetch';
import { Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import { IconEye } from '@tabler/icons-react';
import useMutate from '@core/hooks/useMutate';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';

/**
 * [K-tool gencode][EN] CheckListTab component for displaying checklist content in WorkflowPage.
 * @returns React component for checklist tab content
 * <AUTHOR> gencode
 * @created_date 2025-07-21
 */
type CheckListTabProps = {
  id?: number;
};

const checkListColumns: ColumnType<ChangeWorkflow>[] = [
  {
    name: 'name',
    title: 'Name',
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    customRender: renderDateTime,
    hidden: true,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
  },
];

export const CheckListTab: React.FC<CheckListTabProps> = () => {
  const changeId = Number(useParams().id);
  const navigate = useNavigate();
  const { data: checkListData, refetch: refetchCheckList } = useFetch(ChangeRequestApi.findAllWorkflows(changeId, ChangeWorkflowTypeEnum.CHECKLIST), {
    enabled: !!changeId,
  });

  const { mutate: deleteCheckListMutate } = useMutate(ChangeRequestApi.deleteWorkflow, {
    successNotification: 'Workflow deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => {
      refetchCheckList();
    },
  });

  const tableProps: KanbanTableProps<ChangeWorkflow> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeWorkflow> = {
      columns: checkListColumns,
      data: checkListData?.data ?? [],
      actions: {
        customAction: (data) => {
          return (
            <Flex gap='xs' align='center'>
              <Tooltip label='View'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(
                      buildChangeDetailUrl(
                        ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL,
                        changeId,
                        data.id || 0,
                        EntityAction.VIEW,
                        ChangeRquestTabsEnum.WORKFLOW_CHECKLIST,
                      ),
                    );
                  }}>
                  <IconEye />
                </KanbanIconButton>
              </Tooltip>
              <Tooltip label='Edit'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(
                      buildChangeDetailUrl(
                        ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL,
                        changeId,
                        data.id || 0,
                        EntityAction.EDIT,
                        ChangeRquestTabsEnum.WORKFLOW_CHECKLIST,
                      ),
                    );
                  }}>
                  <IconEdit />
                </KanbanIconButton>
              </Tooltip>
              <Tooltip label='Delete'>
                <KanbanIconButton
                  variant='transparent'
                  color='red'
                  size={'sm'}
                  onClick={() => {
                    deleteCheckListMutate({ changeId: changeId, workflowId: data.id || 0 }, { confirm: deleteConfirm([data.name || '']) });
                  }}>
                  <IconTrash />
                </KanbanIconButton>
              </Tooltip>
            </Flex>
          );
        },
      },
    };
    return tblProps;
  }, [changeId, deleteCheckListMutate, navigate, checkListData?.data]);

  return (
    <div className={stylesCss.workflowPage}>
      <KanbanButton
        size='xs'
        variant='light'
        onClick={() =>
          navigate(
            buildChangeDetailUrl(
              ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL,
              changeId,
              0,
              EntityAction.CREATE,
              ChangeRquestTabsEnum.WORKFLOW_CHECKLIST,
            ),
          )
        }
        className={stylesCss.createButton}
        leftSection={<IconPlus size={10} />}>
        Create checklist
      </KanbanButton>
      {checkListData?.data?.length !== 0 && <KanbanTable {...tableProps} />}
      {checkListData?.data?.length === 0 && <KanbanText>No checklist yet. Click the button to create a new checklist</KanbanText>}
    </div>
  );
};
