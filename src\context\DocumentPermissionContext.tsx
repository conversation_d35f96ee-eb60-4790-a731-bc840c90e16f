import React, { createContext, useContext, useMemo } from 'react';
import { ChangeRequestDocumentGroupModel, ChangeRequestDocumentRole } from '@models/ChangeRequestDocumentGroupModel';

interface DocumentContextValue {
  documentGroups: ChangeRequestDocumentGroupModel[];
  roles: ChangeRequestDocumentRole[];
  openedItems: string[];
  setOpenedItems: React.Dispatch<React.SetStateAction<string[]>>;
  focusAccordionId?: string | null;
  hasRole: (role: ChangeRequestDocumentRole) => boolean;
}

const DocumentContext = createContext<DocumentContextValue | null>(null);

interface DocumentContextProps {
  documentGroups: ChangeRequestDocumentGroupModel[];
  roles: ChangeRequestDocumentRole[];
  openedItems: string[];
  setOpenedItems: React.Dispatch<React.SetStateAction<string[]>>;
  focusAccordionId?: string | null;
  children: React.ReactNode;
}

export const DocumentProvider: React.FC<DocumentContextProps> = ({
  children,
  documentGroups,
  focusAccordionId,
  openedItems,
  roles,
  setOpenedItems,
}) => {
  const value = useMemo(
    () => ({
      openedItems,
      roles,
      documentGroups,
      focusAccordionId,
      setOpenedItems,
      hasRole: (role: ChangeRequestDocumentRole) => roles.includes(role),
    }),
    [openedItems, roles, documentGroups, focusAccordionId, setOpenedItems],
  );

  return <DocumentContext.Provider value={value}>{children}</DocumentContext.Provider>;
};

export const useDocumentContext = () => {
  const ctx = useContext(DocumentContext);
  if (!ctx) {
    throw new Error('useDocumentPermission must be used within PermissionProvider');
  }
  return ctx;
};
