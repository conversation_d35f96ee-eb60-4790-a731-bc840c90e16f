import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from '@api/Utils';
import { createResponseSchema, ResponseData } from '@core/schema/Common';
import {
  ChangeRequestDocumentApproverRejectRequest,
  ChangeRequestDocumentGroupCoordinatorRequest,
  ChangeRequestDocumentGroupOwnerRequest,
  ChangeRequestDocumentGroupWithRoleModel,
  ChangeRequestDocumentGroupWithRoleModelSchema,
  ChangeRequestSendToApproverRequest,
  ChangeRequestSendToOwnerRequest,
  ChangeRequestTodoSidebarSchema,
} from '@models/ChangeRequestDocumentGroupModel';
import { RequestConfig } from '@core/api';
import { z } from 'zod';

export class ChangeRequestDocumentGroupApi {
  static findTodoSidebar(changeRequestId: number) {
    return createRequest({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}`,
      method: 'GET',
      schema: createResponseSchema(z.array(ChangeRequestTodoSidebarSchema)),
    });
  }

  static findAll(changeRequestId: number): RequestConfig<ResponseData<ChangeRequestDocumentGroupWithRoleModel>> {
    return createRequest({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}/details`,
      method: 'GET',
      schema: createResponseSchema(ChangeRequestDocumentGroupWithRoleModelSchema),
    });
  }

  static saveOrUpdateForCoordinator(changeRequestId: number, data: ChangeRequestDocumentGroupCoordinatorRequest[]) {
    const formData = new FormData();

    data.forEach((group) => {
      const documents = group.documentGroup?.documents ?? [];
      documents.forEach((doc) => {
        if (doc.file instanceof File && doc.tempId !== null && doc.tempId !== undefined) {
          formData.append(doc.tempId, doc.file);
        }
      });
    });

    // Generate JSON metadata (leave file)
    const metadata = data.map((group) => ({
      ...group,
      documentGroup: group.documentGroup
        ? {
            ...group.documentGroup,
            documents: group.documentGroup.documents.map(({ file: _file, ...rest }) => rest),
          }
        : undefined,
    }));

    formData.append('request', new Blob([JSON.stringify(metadata)], { type: 'application/json' }));

    return createRequest({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}/coordinator`,
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  static saveOrUpdateForOwner(changeRequestId: number, data: ChangeRequestDocumentGroupOwnerRequest[]) {
    const formData = new FormData();

    data.forEach((group) => {
      const documents = group.documents ?? [];
      documents.forEach((doc) => {
        if (doc.file instanceof File && doc.tempId !== null && doc.tempId !== undefined) {
          formData.append(doc.tempId, doc.file);
        }
      });
    });

    const metadata = data.map(({ documents, ...rest }) => ({
      ...rest,
      documents: documents.map(({ file: _file, ...restDoc }) => restDoc),
    }));
    formData.append('request', new Blob([JSON.stringify(metadata)], { type: 'application/json' }));

    return createRequest({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}/owner`,
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  static downloadFile(changeRequestId: number, documentGroupId: number): RequestConfig<Blob> {
    return createRequest<Blob>({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}/downloads`,
      params: {
        changeRequestDocumentId: documentGroupId,
      },
      method: 'GET',
      responseType: 'blob',
    });
  }

  static sendToOwner(changeRequestId: number, request: ChangeRequestSendToOwnerRequest[]) {
    return createRequest({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}/actions/send-to-owner`,
      method: 'POST',
      data: request,
    });
  }

  static sendToApprover(changeRequestId: number, request: ChangeRequestSendToApproverRequest[]) {
    return createRequest({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}/actions/send-to-approver`,
      method: 'POST',
      data: request,
    });
  }

  static approveOrReject(changeRequestId: number, request: ChangeRequestDocumentApproverRejectRequest) {
    return createRequest({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}/actions`,
      method: 'POST',
      data: request,
    });
  }

  static submitDocumentApprover(changeRequestId: number) {
    return createRequest({
      url: `${BaseURL.changeRequestDocuments(changeRequestId)}/actions/submit-document-approver`,
      method: 'POST',
    });
  }
}
