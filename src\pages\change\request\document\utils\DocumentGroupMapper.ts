import {
  ChangeRequestDocumentForm,
  ChangeRequestDocumentGroupCoordinatorRequest,
  ChangeRequestDocumentGroupForm,
  ChangeRequestDocumentGroupModel,
  ChangeRequestDocumentOwnerForm,
  ChangeRequestDocumentApproverForm,
  ChangeRequestDocumentGroupOwnerRequest,
} from '@models/ChangeRequestDocumentGroupModel';

export function mapChangeRequestDocumentGroupModelToForm(groups: ChangeRequestDocumentGroupModel[]): ChangeRequestDocumentGroupForm[] {
  return groups.map((group) => ({
    documentGroupId: group.id,
    documentGroupName: group.name,
    owners: group.owners.map<ChangeRequestDocumentOwnerForm>((owner) => ({
      username: owner.username,
      displayName: owner.displayName,
      status: owner.status,
      isActive: owner.isActive,
      documents:
        owner.documents && owner.documents.length > 0
          ? owner.documents?.map<ChangeRequestDocumentForm>((doc) => ({
              tempId: undefined,
              id: doc.id,
              type: doc.type,
              file: undefined,
              documentUrl: doc.documentUrl || undefined,
              documentName: doc.documentName || undefined,
              status: doc.status,
              approvers: doc.approvers.map<ChangeRequestDocumentApproverForm>((appr) => ({
                username: appr.user.username,
                displayName: appr.user.displayName,
                documentApproverLevel: appr.documentApproverLevel,
              })),
            }))
          : undefined,
    })),
    cabs:
      group.approvers && group.approvers.length > 0
        ? group.approvers?.map((appr) => ({
            username: appr.user.username,
            displayName: appr.user.displayName,
          }))
        : undefined,
  }));
}

export function mapFormToCoordinatorRequests(forms: ChangeRequestDocumentGroupForm[]): ChangeRequestDocumentGroupCoordinatorRequest[] {
  return forms.map((formGroup) => ({
    id: formGroup.documentGroupId,
    name: formGroup.documentGroupName,
    owners: formGroup.owners.flatMap((owner) => owner.username),
    documentGroup: formGroup.owners.some((owner) => (owner.documents ?? []).length > 0)
      ? {
          id: undefined,
          documents: formGroup.owners.flatMap((owner) =>
            (owner.documents ?? []).map((doc) => ({
              tempId: doc.tempId,
              id: doc.id ?? undefined,
              type: doc.type,
              file: doc.file,
              documentUrl: doc.documentUrl,
              documentName: doc.documentName,
              approvers: doc.approvers.map((ap) => ({
                username: ap.username,
                documentApproverLevel: ap.documentApproverLevel,
              })),
            })),
          ),
          approvers: (formGroup.cabs ?? []).map((cab) => cab.username),
        }
      : undefined,
    ownerToChange: formGroup.ownerToChange,
  }));
}

export function mapFormToOwnerRequests(forms: ChangeRequestDocumentGroupForm[], currentUsername: string): ChangeRequestDocumentGroupOwnerRequest[] {
  return (
    (forms ?? [])
      // chỉ giữ group có owner là currentUsername
      .filter((fg) => (fg.owners ?? []).some((o) => o.username === currentUsername))
      .map((fg) => ({
        id: fg.documentGroupId ?? null,
        documents: (fg.owners ?? [])
          .filter((owner) => owner.username === currentUsername)
          .flatMap((owner) => owner.documents ?? [])
          .map((doc) => ({
            tempId: doc.tempId,
            id: doc.id ?? null,
            type: doc.type,
            file: doc.file,
            documentUrl: doc.documentUrl,
            documentName: doc.documentName,
            approvers: (doc.approvers ?? []).map((ap) => ({
              username: ap.username,
              documentApproverLevel: ap.documentApproverLevel,
            })),
          })),
        approvers: Array.from(new Set((fg.cabs ?? []).map((c) => c.username).filter(Boolean))),
      }))
  );
}
