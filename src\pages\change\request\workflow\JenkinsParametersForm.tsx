/* K-tool gencode */
import React from 'react';
import { Stack, Flex } from '@mantine/core';
import { KanbanCheckbox, KanbanInput, KanbanSelect } from 'kanban-design-system';
import styles from './ParametersTab.module.scss';
import { JenkinsParameter } from './reactFlow/CustomNode';

export interface JenkinsParametersFormProps {
  parameters: JenkinsParameter[];
  values?: Record<string, string | number | boolean>;
  onChange: (name: string, value: string | number | boolean) => void;
}

const JenkinsParametersForm: React.FC<JenkinsParametersFormProps> = ({ onChange, parameters, values }) => {
  return (
    <Stack gap='md'>
      {parameters.map((param, index) => {
        const currentValue = values?.[param.name] ?? param.defaultValue ?? '';

        switch (param.type) {
          case 'StringParameterDefinition':
          case 'TextParameterDefinition':
            return (
              <KanbanInput
                key={index}
                label={param.name}
                description={param.description}
                value={`${currentValue}`}
                onChange={(e) => onChange(param.name, e.target.value)}
                maxLength={param.type === 'TextParameterDefinition' ? 1000 : 255}
                required
                className={styles.paramTextInput}
              />
            );

          case 'BooleanParameterDefinition':
            return (
              <Flex align='center' gap='md'>
                <KanbanCheckbox
                  key={index}
                  checked={!!values?.[param.name]}
                  onChange={(e) => onChange(param.name, e.target.checked)}
                  className={styles.paramCheckbox}
                  label={param.name}
                />
              </Flex>
            );

          case 'ChoiceParameterDefinition':
            return (
              <KanbanSelect
                key={index}
                label={param.name}
                description={param.description}
                data={param.choices?.map((c) => ({ value: c, label: c })) || []}
                value={`${currentValue}`}
                onChange={(v) => onChange(param.name, `${v}`)}
                required
                className={styles.paramTextInput}
                placeholder='Select an option'
              />
            );

          default:
            return (
              <KanbanInput
                key={index}
                label={`${param.name} (${param.type})`}
                description={param.description}
                value={`${currentValue}`}
                onChange={(e) => onChange(param.name, e.target.value)}
                maxLength={255}
                className={styles.paramTextInput}
              />
            );
        }
      })}
    </Stack>
  );
};

export default JenkinsParametersForm;
