/* Modal and container styles */
.modalSubtitle {
  margin: 0;
  color: var(--mantine-color-dimmed);
  font-size: 12px;
}

.treeContainer {
  border-radius: var(--mantine-radius-md);
  border: 1px solid var(--mantine-color-gray-3);
  background: var(--mantine-color-white);
  padding: 12px;
  min-height: 300px;
  max-height: 380px;
  overflow-y: auto;
  position: relative;
}

/* Browse button */
.browseButton {
  min-width: 120px;
  margin-bottom: 3%;
}

/* States */
.loadingState,
.errorState,
.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loadingState {
  color: var(--mantine-color-dimmed);
  font-size: 14px;
}

.loadingCenter {
  text-align: center;
}

.spinnerSm {
  width: 24px;
  height: 24px;
  border: 3px solid var(--mantine-color-gray-2);
  border-top: 3px solid var(--mantine-color-blue-6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

.errorState {
  color: var(--mantine-color-red-6);
  font-size: 14px;
  text-align: center;
}

.errorIcon {
  margin-bottom: 8px;
}

.retryBtn {
  margin-top: 8px;
}

.emptyState {
  color: var(--mantine-color-gray-5);
  font-size: 14px;
}

/* Footer */
.modalFooter {
  margin-top: 12px;
  padding: 8px;
  background: var(--mantine-color-gray-0);
  border-radius: var(--mantine-radius-sm);
  font-size: 11px;
  color: var(--mantine-color-dimmed);
  text-align: center;
}

/* Tree node row styles - không có background khác biệt */
.treeNodeRow {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--mantine-radius-sm);
  transition: background-color 0.15s ease;
  margin-bottom: 2px;

  &:hover {
    background: var(--mantine-color-gray-0);
  }
}

/* Expansion toggle - đơn giản */
.expandToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: var(--mantine-radius-sm);
  transition: all 0.15s ease;
  cursor: pointer;
  background: var(--mantine-color-gray-1);

  &:hover {
    background: var(--mantine-color-gray-2);
  }
}

/* Spinner tiny for node */
.spinnerXs {
  width: 10px;
  height: 10px;
  border: 2px solid var(--mantine-color-gray-6);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Node label */
.nodeLabel {
  user-select: none;
  flex: 1;
  margin-left: 8px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: var(--mantine-color-text);
}

/* Badges - tối giản */
.badgeJob {
  padding: 2px 6px;
  background: var(--mantine-color-green-0);
  color: var(--mantine-color-green-7);
  font-size: 10px;
  font-weight: 500;
  border-radius: var(--mantine-radius-xl);
  text-transform: uppercase;
}

.badgeMultiBranch {
  padding: 2px 6px;
  background: var(--mantine-color-violet-0);
  color: var(--mantine-color-violet-7);
  font-size: 10px;
  font-weight: 500;
  border-radius: var(--mantine-radius-xl);
  text-transform: uppercase;
}