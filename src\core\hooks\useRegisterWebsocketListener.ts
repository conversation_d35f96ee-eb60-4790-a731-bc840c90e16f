import { getWebSocketService } from '@api/WebSocketService';
import { WebsocketData } from '@core/schema/WebsocketData';
import { useSafetyRenderStateRef } from 'kanban-design-system';
import { useEffect } from 'react';

const useRegisterWebsocketListener = (callback: (data: WebsocketData) => void) => {
  const websocketService = getWebSocketService();
  const callbackRef = useSafetyRenderStateRef(callback);

  useEffect(() => {
    const stableCallback = (data: WebsocketData) => {
      callbackRef.current?.(data);
    };

    if (websocketService) {
      websocketService.registerNotificationHandler(stableCallback);
    }

    return () => {
      if (websocketService) {
        websocketService.unregisterNotificationHandler(stableCallback);
      }
    };
  }, [callbackRef, websocketService]);
};

export default useRegisterWebsocketListener;
