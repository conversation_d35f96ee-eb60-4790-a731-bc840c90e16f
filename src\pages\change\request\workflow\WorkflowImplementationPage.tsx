import { EntityAction } from '@common/constants/EntityActionConstants';
import { buildChangeDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTable,
  KanbanTableProps,
  KanbanText,
  renderDateTime,
  getDefaultTableAffected,
} from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import stylesCss from './WorkflowPage.module.scss';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { ChangeWorkflow, ChangeWorkflowTypeEnum } from '@core/schema/ChangeWorkflowNode';
import useFetch from '@core/hooks/useFetch';
import { Flex, Tooltip } from '@mantine/core';
import { KanbanModal, KanbanInput, KanbanSelect } from 'kanban-design-system';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import { IconEye } from '@tabler/icons-react';
import useMutate from '@core/hooks/useMutate';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import ComboboxLoadMore, { ComboboxLoadMoreProps } from '@components/ComboboxLoadMore';
import { GroupApi } from '@api/GroupApi';
import { ChangeFlowNodeGroupModel } from '@models/ChangeFlowModel';

type WorkFlowPageProps = {
  id?: number;
};

const columns: ColumnType<ChangeWorkflow>[] = [
  {
    name: 'name',
    title: 'Name',
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    customRender: renderDateTime,
    hidden: true,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
  },
];

/* Created by: K-tool gencode */
export const WorkflowImplementationPage: React.FC<WorkFlowPageProps> = (props) => {
  const changeId = Number(useParams().id);
  const navigate = useNavigate();
  const { data: workflows, refetch: refetchList } = useFetch(ChangeRequestApi.findAllWorkflows(changeId), {
    enabled: !!changeId,
  });

  const { mutate: deleteWorkflowMutate } = useMutate(ChangeRequestApi.deleteWorkflow, {
    successNotification: 'Workflow deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => {
      refetchList();
    },
  });

  // State for create workflow modal
  const [openedCreateModal, setOpenedCreateModal] = useState(false);
  const [workflowName, setWorkflowName] = useState('');
  const [type, setType] = useState('');

  // State for selected groups in ComboboxLoadMore
  const [assignGroups, setAssignGroups] = useState<ChangeFlowNodeGroupModel[]>([]);

  //TODO remove
  useEffect(() => {
    refetchList();
  }, [refetchList, props]);

  const tableProps: KanbanTableProps<ChangeWorkflow> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeWorkflow> = {
      columns: columns,
      data: workflows?.data ?? [],
      actions: {
        customAction: (data) => {
          return (
            <Flex gap='xs' align='center'>
              <Tooltip label='View'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(
                      buildChangeDetailUrl(
                        ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL,
                        changeId,
                        data.id || 0,
                        EntityAction.VIEW,
                        ChangeRquestTabsEnum.WORKFLOW_IMPLEMENTATION,
                      ),
                    );
                  }}>
                  <IconEye />
                </KanbanIconButton>
              </Tooltip>
              <Tooltip label='Edit'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(
                      buildChangeDetailUrl(
                        ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL,
                        changeId,
                        data.id || 0,
                        EntityAction.EDIT,
                        ChangeRquestTabsEnum.WORKFLOW_IMPLEMENTATION,
                      ),
                    );
                  }}>
                  <IconEdit />
                </KanbanIconButton>
              </Tooltip>
              <Tooltip label='Delete'>
                <KanbanIconButton
                  variant='transparent'
                  color='red'
                  size={'sm'}
                  onClick={() => {
                    deleteWorkflowMutate({ changeId: changeId, workflowId: data.id || 0 }, { confirm: deleteConfirm([data.name || '']) });
                  }}>
                  <IconTrash />
                </KanbanIconButton>
              </Tooltip>
            </Flex>
          );
        },
      },
    };
    return tblProps;
  }, [changeId, deleteWorkflowMutate, navigate, workflows?.data]);

  // Handler for submit create workflow (chỉ demo, cần tích hợp API thực tế nếu có)
  const handleCreateWorkflow = () => {
    // TODO: Gọi API tạo workflow ở đây
    setOpenedCreateModal(false);
    setWorkflowName('');
    setType('');
    // refetchList(); // Nếu cần reload lại danh sách
  };

  // Sửa lỗi: TableAffactedSafeType không tồn tại, dùng kiểu any hoặc đúng kiểu nếu có
  const [serviceSearchParams, setServiceSearchParams] = useState<any>({
    ...getDefaultTableAffected(),
    sortedBy: 'name',
  });

  const { fetchNextPage: fetchNextPageApplication, flatData: applicationData } = useInfiniteFetch(
    GroupApi.findAllGroups({
      ...serviceSearchParams,
    }),
    {
      showLoading: false,
      enabled: true,
    },
  );

  const renderApplicationPill = useCallback<ComboboxLoadMoreProps<ChangeFlowNodeGroupModel>['renderPillLabel']>(
    (application) => application.name,
    [],
  );

  return (
    <div className={stylesCss.workflowPage}>
      <KanbanButton
        size='xs'
        variant='light'
        onClick={() => setOpenedCreateModal(true)}
        className={stylesCss.createButton}
        leftSection={<IconPlus size={10} />}>
        Create workflow
      </KanbanButton>
      <KanbanModal
        title='Create Workflow'
        opened={openedCreateModal}
        onClose={() => setOpenedCreateModal(false)}
        actions={
          <Flex gap='sm'>
            <KanbanButton variant='filled' onClick={handleCreateWorkflow}>
              Create
            </KanbanButton>
          </Flex>
        }
        size='40%'>
        <KanbanInput label='Workflow Name' value={workflowName} onChange={(e) => setWorkflowName(e.target.value)} required mb='md' />
        {/* State value for selected groups */}
        <ComboboxLoadMore
          options={applicationData.map((item: any) => ({
            id: String(item.id),
            name: item.name,
          }))}
          onChange={setAssignGroups}
          required
          label='Groups User'
          onSearch={(val: string) => setServiceSearchParams((prev: any) => ({ ...(prev ?? {}), name: val }))}
          onScroll={fetchNextPageApplication}
          renderPillLabel={renderApplicationPill}
          renderOptionLabel={(data: any) => data.name}
          values={assignGroups?.map((v: any) => ({ id: String(v.id), name: v.name })) ?? []}
          scrollableForValue
        />
        <KanbanSelect label='Type' data={Object.values(ChangeWorkflowTypeEnum)} value={type} onChange={(value) => setType(value ?? '')} required />
      </KanbanModal>
      {workflows?.data?.length !== 0 && <KanbanTable {...tableProps} />}
      {workflows?.data?.length === 0 && <KanbanText>No workflow yet. Click the button to create a new workflow</KanbanText>}
    </div>
  );
};
