/* Reviewed by: K-tool gencode */
import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { createResponseSchema } from '@core/schema/Common';
import { ChangeRequestWorkflowExecute, WorkflowExecutionTypeEnum } from '@core/schema/ChangeRequestWorkflowExecute';
import { ChangeRequestWorkflowDetailExecute, ChangeRequestWorkflowDetailExecuteSchema } from '@core/schema/ChangeRequestWorkflowDetailExecute';
import { ResponseData } from '@core/schema/Common';
import { ConfirmStatusManualNodeEnum } from '@pages/change/request/workflow/RunNodeManualButton';

/* Created by: K-tool gencode */
export class ChangeRequestWorkflowApi {
  static findAll(changeRequestId: number): RequestConfig<ResponseData<ChangeRequestWorkflowExecute[]>> {
    return {
      url: `${BaseURL.changeRequest}/${changeRequestId}/workflows`,
      method: 'GET',
    };
  }

  static findById(changeRequestId: number, id: number): RequestConfig<ResponseData<ChangeRequestWorkflowExecute>> {
    return {
      url: `${BaseURL.changeRequest}/${changeRequestId}/workflows/execution/${id}`,
      method: 'GET',
    };
  }

  static execution(data: {
    changeId: number;
    workflowId: number;
    type: WorkflowExecutionTypeEnum;
  }): RequestConfig<ResponseData<ChangeRequestWorkflowExecute>> {
    return {
      url: `${BaseURL.changeRequest}/${data.changeId}/workflows/${data.workflowId}/executions`,
      params: { type: data.type },
      method: 'POST',
    };
  }

  static findExecutionLastest(changeId: number, workflowId: number): RequestConfig<ResponseData<ChangeRequestWorkflowExecute>> {
    return {
      url: `${BaseURL.changeRequest}/${changeId}/workflows/${workflowId}/executions/latest`,
      method: 'GET',
    };
  }

  static delete(changeRequestId: number, id: number): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.changeRequest}/${changeRequestId}/workflow-execute/${id}`,
      method: 'DELETE',
    };
  }
  static executionNode(data: {
    changeId: number;
    workflowId: number;
    nodeId: string;
  }): RequestConfig<ResponseData<ChangeRequestWorkflowDetailExecute>> {
    return {
      url: `${BaseURL.changeRequest}/${data.changeId}/workflows/node-execution`,
      method: 'POST',
      data: data,
      schema: createResponseSchema(ChangeRequestWorkflowDetailExecuteSchema),
    };
  }

  static updateStatus(data: {
    changeId: number;
    workflowId: number;
    nodeId: string;
    status: ConfirmStatusManualNodeEnum;
  }): RequestConfig<ResponseData<ChangeRequestWorkflowDetailExecute>> {
    return {
      url: `${BaseURL.changeRequest}/${data.changeId}/workflows/execution/status`,
      method: 'PUT',
      data: data,
      schema: createResponseSchema(ChangeRequestWorkflowDetailExecuteSchema),
    };
  }
}
