import { isAuthorized } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@core/schema/AclPermission';
import { useGetCurrentUser } from './useGetCurrentUser';
//ft/role function authorize
export const useCheckPermissons = (requirePermissions: AclPermission[], allMatchPermissions = false) => {
  const currentUser = useGetCurrentUser();

  if (!currentUser) {
    return false;
  }
  return isAuthorized(currentUser.userInfo?.aclPermissions || [], requirePermissions, allMatchPermissions);
  //todo bloom filter
};
