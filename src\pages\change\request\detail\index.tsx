import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Box, Flex, Grid, Paper } from '@mantine/core';
import { getDefaultTableAffected, KanbanButton, KanbanIconButton, KanbanTitle, SortOrder, TableAffactedSafeType } from 'kanban-design-system';
import React, { Dispatch, forwardRef, SetStateAction, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { IconArrowLeft } from '@tabler/icons-react';
import {
  ChangeRequestFieldItem,
  ChangeRequestModel,
  ChangeRequestModelSchema,
  convertTemplateFieldsToChangeRequestFields,
} from '@core/schema/ChangeRequest';
import { zodResolver } from '@hookform/resolvers/zod';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { Layout } from 'react-grid-layout';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { BREAK_FIELD_MIN_HEIGHT, FIELD_MAX_HEIGHT } from '@common/constants/ChangeTemplateConstants';
import { ChangeTemplate, TemplateCustomFieldTypeEnum } from '@core/schema/ChangeTemplate';
import { ChangeTemplateApi } from '@api/ChangeTemplateApi';
import useMutate from '@core/hooks/useMutate';
import { getQueryKey } from '@common/utils/QueryUtils';
import { useQueryClient } from '@tanstack/react-query';
import { useDisclosure } from '@mantine/hooks';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import { buildDetailUrl, buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { EntityAction } from '@common/constants/EntityActionConstants';
import ChangeRequestGrid from '@pages/change/request/components/ChangeRequestGrid';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { PermissionAction, PermissionActionModule } from '@common/constants/AclPermissionConstants';
import { SelectWithPage } from '@components/SelectWithPage';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { callRequest } from '@core/api';
import { DEFAULT_CHANGE_REQUEST } from '@pages/change/request/ChangeRequestDetailPage';
import { ChangeRquestTabsEnum, CONTENT_REQUIRED_FIELD } from '@common/constants/ChangeRequestConstants';
import { NoticeChangeRequest } from '../components/NoticeChangeRequest';
import { ChangeRequestTabRef } from '../ChangeRequestTabRef';
import { useGetCurrentUser } from '@core/hooks/useGetCurrentUser';

interface ChangeRequestDetailTabProps {
  setChangeTemplateId: Dispatch<SetStateAction<number>>;
  changeRequest: ChangeRequestModel;
}

const ChangeRequestDetailTab = forwardRef<ChangeRequestTabRef, ChangeRequestDetailTabProps>(function ChangeRequestDetailTabComponent(
  { changeRequest, setChangeTemplateId },
  ref,
) {
  const methods = useForm<ChangeRequestModel>({
    defaultValues: DEFAULT_CHANGE_REQUEST,
    resolver: zodResolver(ChangeRequestModelSchema),
    mode: 'onBlur',
  });

  const [changeRequestCurrent, setChangeRequestCurrent] = useState(changeRequest);
  const { control, formState, getValues, reset, setError } = methods;
  const { errors } = formState;
  const [changeRequestId, setChangeRequestId] = useState(Number(useParams().id));
  const isUpdateMode = !!changeRequestId;
  const { search } = useLocation();
  const queryParams = new URLSearchParams(search);
  const [isViewMode, setIsViewMode] = useState(queryParams.get('action') === EntityAction.VIEW);
  const queryClient = useQueryClient();
  const detailQueryConfig = ChangeRequestApi.findWithId(changeRequestId);
  const getInitialTemplateSearchParams = (): TableAffactedSafeType => {
    return {
      ...getDefaultTableAffected(),
      sortedBy: 'name',
      sortOrder: SortOrder.ASC,
      advancedFilterMapping: {
        ['isActive']: {
          filterOption: 'equals',
          value: { fromValue: 1 },
        },
      },
    };
  };

  const [templateSearchParams, setTemplateSearchParams] = useState<TableAffactedSafeType>({
    ...getInitialTemplateSearchParams(),
  });

  const {
    data: changeTemplatesResponse,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useInfiniteFetch(
    ChangeTemplateApi.findAll({
      ...templateSearchParams,
    }),
    {
      enabled: true,
      refetchOnWindowFocus: false,
      showLoading: false,
    },
  );
  useEffect(() => {
    refetch();
  }, [refetch, templateSearchParams]);
  const changeTemplateOptions = useMemo(
    () =>
      changeTemplatesResponse?.pages.flatMap(
        (page) =>
          page.data?.content?.map((template) => ({
            value: template.name,
            label: template.name,
          })) || [],
      ) || [],
    [changeTemplatesResponse],
  );

  const [changeTemplate, setChangeTemplate] = useState<ChangeTemplate | null>();
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [savedSuccess, setSavedSuccess] = useState(false);
  const navigate = useNavigate();
  const handleNavigateToList = useCallback(() => {
    navigate(buildUrl(ROUTE_PATH.CHANGE_REQUEST), { state: buildNavigateState({ fromDetail: true }) });
  }, [navigate]);
  const handleNavigateToViewDetail = useCallback(() => {
    if (isViewMode) {
      navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.VIEW));
    }
  }, [changeRequestId, isViewMode, navigate]);
  const userInfo = useGetCurrentUser().userInfo;
  const templateName = useWatch({
    control: methods.control,
    name: 'templateName',
  });

  const [layouts, setLayouts] = useState<Layout[]>([]);

  const updateLayoutsFromFields = useCallback((fields: ChangeRequestFieldItem[]) => {
    const appendLayouts = fields
      .map((sourceItem) => {
        if (!sourceItem) {
          console.error('Source item is undefined or null.');
          return;
        }

        return {
          i: (sourceItem.customFieldId || 0).toString(),
          x: sourceItem.horizontalCoordinate || 0,
          y: sourceItem.verticalCoordinate || 0,
          w: sourceItem.width || 1,
          h: sourceItem.height || 1,
          minW: sourceItem.width || 1,
          maxW: sourceItem.width || 1,
          minH: sourceItem.customFieldType !== TemplateCustomFieldTypeEnum.Enum.BREAK ? 1 : BREAK_FIELD_MIN_HEIGHT,
          maxH: FIELD_MAX_HEIGHT,
        };
      })
      .filter((it) => !!it);
    setLayouts(appendLayouts);
  }, []);

  useEffect(() => {
    if (changeRequestCurrent || changeTemplate) {
      const hasNewTemplate = !!changeTemplate?.fields?.length;
      const fieldsToUse = hasNewTemplate ? convertTemplateFieldsToChangeRequestFields(changeTemplate.fields || []) : changeRequestCurrent.fields;
      reset({
        id: changeRequestCurrent.id,
        templateName: hasNewTemplate ? changeTemplate.name : changeRequestCurrent.templateName,
        changeTemplateId: hasNewTemplate ? changeTemplate.id : changeRequestCurrent.changeTemplateId,
        title: hasNewTemplate ? changeTemplate.name : changeRequestCurrent.title,
        stage: changeRequestCurrent.stage,
        description: changeRequestCurrent.description,
        fields: fieldsToUse,
        notice: hasNewTemplate ? changeTemplate.notice : changeRequestCurrent.notice,
      });
      updateLayoutsFromFields(fieldsToUse || []);
      setChangeTemplateId(hasNewTemplate ? changeTemplate.id : changeRequestCurrent.changeTemplateId);
    }
  }, [changeRequestCurrent, changeTemplate, reset, setChangeTemplateId, updateLayoutsFromFields]);
  const { mutate: saveMutate } = useMutate(ChangeRequestApi.saveOrUpdate, {
    successNotification: `Change saved successfully`,
    errorNotification: (data) => ({ message: data.message }),
    onSuccess: (response) => {
      if (response.data?.id) {
        setChangeRequestId(response.data?.id);
        setIsViewMode(true);
        reset(getValues());
      }
      queryClient.invalidateQueries({ queryKey: getQueryKey(detailQueryConfig) });
      setSavedSuccess(true);
    },
  });
  useEffect(() => {
    if (savedSuccess) {
      handleNavigateToViewDetail();
    }
  }, [savedSuccess, handleNavigateToViewDetail]);

  const handleExistWithoutSave = useCallback(() => {
    const touched = formState.isDirty;
    if (touched && !isViewMode) {
      openModal();
    } else {
      if (!!changeRequestId && !isViewMode) {
        navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.VIEW));
        setIsViewMode(true);
      } else {
        handleNavigateToList();
      }
    }
  }, [changeRequestId, formState.isDirty, handleNavigateToList, isViewMode, navigate, openModal]);

  const validateField = useCallback(
    (sourceItem: ChangeRequestFieldItem, index: number) => {
      if (!sourceItem) {
        return;
      }

      const isRequired = sourceItem.required;
      if (sourceItem.customFieldType === TemplateCustomFieldTypeEnum.enum.PICKLIST) {
        const picklistOptions = Array.from(sourceItem.picklistOptions?.values?.() ?? []);
        const isEmpty = picklistOptions.length === 0 || !picklistOptions.some((item) => item.value !== '');
        if (isRequired && isEmpty) {
          setError(`fields.${index}.picklistOptions`, {
            type: 'manual',
            message: CONTENT_REQUIRED_FIELD,
          });
        }
      } else {
        const defaultValue = sourceItem.fieldConstraint?.defaultValue;
        if (isRequired && (!defaultValue || defaultValue.trim() === '')) {
          setError(`fields.${index}.fieldConstraint.defaultValue`, {
            type: 'manual',
            message: CONTENT_REQUIRED_FIELD,
          });
        }
      }
    },
    [setError],
  );

  const handleSave = useCallback(async () => {
    getValues().fields?.forEach((field, index) => {
      validateField(field, index);
    });

    if (templateName === '' || undefined) {
      setError(`templateName`, {
        type: 'manual',
        message: CONTENT_REQUIRED_FIELD,
      });
    }

    if (Object.keys(errors).length > 0) {
      return;
    }

    saveMutate({
      ...getValues(),
      id: changeRequestCurrent.id,
      changeTemplateId: changeTemplate ? changeTemplate.id : changeRequestCurrent.changeTemplateId,
      templateName: changeTemplate ? changeTemplate.name : changeRequestCurrent.templateName,
      stage: changeRequestCurrent.stage,
      coordinator: userInfo?.userName,
    });
  }, [
    getValues,
    templateName,
    errors,
    saveMutate,
    changeRequestCurrent.id,
    changeRequestCurrent.changeTemplateId,
    changeRequestCurrent.templateName,
    changeRequestCurrent.stage,
    changeTemplate,
    userInfo?.userName,
    validateField,
    setError,
  ]);

  const canEditChange = useCheckPermissons([{ module: PermissionActionModule.CHANGE_SDP, permission: PermissionAction.CHANGE_UPDATE }]);

  const handleTemplateSearch = useCallback((val?: string) => {
    const value = val ?? '';
    setTemplateSearchParams((prev) => ({
      ...(prev ?? {}),
      sortedBy: 'name',
      sortOrder: SortOrder.ASC,
      advancedFilterMapping: {
        ...(prev?.advancedFilterMapping ?? {}),
        ['name']: {
          filterOption: 'contains',
          value: {
            fromValue: value,
          },
        },
      },
    }));
  }, []);

  const handleChangeTemplate = useCallback(
    async (templateName: string) => {
      const selected = changeTemplatesResponse?.pages.flatMap((page) => page.data?.content || []).find((template) => template.name === templateName);

      if (!selected) {
        return;
      }

      const detailResponse = await callRequest(ChangeTemplateApi.getDetail(selected.id));
      if (detailResponse?.data) {
        setChangeTemplate(detailResponse.data);
        setTemplateSearchParams(getInitialTemplateSearchParams());
        const fields = convertTemplateFieldsToChangeRequestFields(detailResponse.data.fields || []);
        methods.setValue('fields', fields);
      }
    },
    [changeTemplatesResponse?.pages, methods],
  );

  const handleClearTemplate = useCallback(() => {
    setChangeTemplate(null);
    setChangeRequestCurrent(DEFAULT_CHANGE_REQUEST);
    setTemplateSearchParams(getInitialTemplateSearchParams());
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      saveTab: () => {
        handleSave();
      },
      isFormDirty: formState.isDirty,
      tab: ChangeRquestTabsEnum.DETAIL,
    }),
    [formState.isDirty, handleSave],
  );

  return (
    <FormProvider {...methods}>
      <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleNavigateToList} />
      <Box p='md'>
        <HeaderTitleComponent
          title={''}
          rightSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanButton size='xs' variant='subtle' onClick={handleExistWithoutSave}>
                Cancel
              </KanbanButton>
              {!isViewMode && (
                <KanbanButton size='xs' variant='filled' type='submit' onClick={handleSave}>
                  Save
                </KanbanButton>
              )}
              {canEditChange && isViewMode && (
                <KanbanButton
                  size='xs'
                  variant='filled'
                  onClick={() => {
                    setIsViewMode(false);
                    navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.EDIT));
                  }}>
                  Edit
                </KanbanButton>
              )}
            </Flex>
          }
          leftSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
                <IconArrowLeft />
              </KanbanIconButton>
              <KanbanTitle fz={'h4'}>{isViewMode ? 'View Change' : isUpdateMode ? 'Edit Change' : 'Add Change'}</KanbanTitle>
            </Flex>
          }
        />

        <Paper withBorder p='md' mb='lg'>
          <SelectWithPage
            label='Change Template'
            required
            disabled={isViewMode}
            options={changeTemplateOptions}
            value={{ value: templateName ?? '', label: templateName ?? '' }}
            error={methods.formState.errors.templateName?.message}
            clearable
            onClear={handleClearTemplate}
            onSearch={handleTemplateSearch}
            isUnselectedWithSeach
            onChange={(val) => {
              if (val) {
                handleChangeTemplate(val);
              }
            }}
            handleScrollToBottom={() => {
              if (hasNextPage && !isFetchingNextPage) {
                fetchNextPage();
              }
            }}
            isLoading={isFetchingNextPage}
          />

          <Grid mb='md'>
            <Grid.Col>
              <ChangeRequestGrid layouts={layouts} action={isViewMode} />
              {!!methods.getValues('fields')?.length && <NoticeChangeRequest control={control} />}
            </Grid.Col>
          </Grid>
        </Paper>
      </Box>
    </FormProvider>
  );
});
export default ChangeRequestDetailTab;
