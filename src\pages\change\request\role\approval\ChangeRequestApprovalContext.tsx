import { createContext, useContext } from 'react';
import { Control } from 'react-hook-form';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { MappedUser } from '@pages/change/request/role/ChangeRequestDetailRole';

export type ChangeRequestApprovalContextType = {
  cabGroupIdx: number;
  cabGroupUserIdx: number;
  changeFlowNodeId: number;
  control: Control<ChangeRequestRoleList>;
  mode: EntityAction;
  modeApproval: ChangeRequestApprovalMode;
  roleIdx: number;
  workflowIdx: number;
  onDelete?: () => void;
  onDeleteUser?: () => void;
  refetchChangeRequestRoles: () => void;
  handleCheckboxChange: (user: MappedUser, checked: boolean) => void;
  handleOpenApprovalResults: () => void;
  onRefetchAll?: () => void;
};

export const ChangeRequestApprovalContext = createContext<ChangeRequestApprovalContextType | null>(null);
export const useChangeRequestApprovalContext = () => {
  const context = useContext(ChangeRequestApprovalContext);
  if (!context) {
    throw new Error('useChangeRequestApprovalContext must be used within a ChangeRequestApprovalProvider');
  }
  return context;
};
