import { Node } from '@xyflow/react';
import { Flex, Group, Paper, Stack, Transition } from '@mantine/core';
import { IconPlus, IconSearch, IconX } from '@tabler/icons-react';
import { KanbanIconButton, KanbanInput, KanbanText } from 'kanban-design-system';
import React, { useCallback, useState } from 'react';
import { NodeSettingsPanel } from './NodeSettingsPanel';
import { ChangeWorkflowNode, ChangeWorkflowTypeEnum } from '@core/schema/ChangeWorkflowNode';
import { getNodeIcon } from './WorkflowDetailPage';
import stylesCss from './WorkflowRightSideBar.module.scss';
import { END_NODE_ID, END_NODE_LABLE, START_NODE_ID, START_NODE_LABLE } from '@common/constants/WorkflowConstants';
import { ChangeRequestWorkflowNodeTypeEnum } from '@common/constants/ChangeWorkflowNodeType';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { changePermissionConfigs } from '@pages/change/changeList';
import { useSearchParams } from 'react-router-dom';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { ChangeWorkflowNodeApi } from '@api/ChangeWorkflowNodeApi';
import { CustomNodeData } from './reactFlow/CustomNode';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import useFetch from '@core/hooks/useFetch';

type NodeWorkflowProps = {
  title: string;
  description?: string;
  icon: string;
  onClick?: () => void;
};

type WorkflowRightSideBarProps = {
  id?: number;
  changeId: number;
  workflowId: number;
  onNodeCreate?: (nodeData: ChangeWorkflowNode) => void;
  selectedNode?: Node;
  onNodeSave: (nodeId: string, data?: CustomNodeData) => void;
  onSettingsClose?: () => void;
  setListNodeOpened: (value: boolean) => void;
  listNodeOpened: boolean;
  nodes: Node[];
  type?: ChangeWorkflowTypeEnum;
};

const NodeWorkflow: React.FC<NodeWorkflowProps> = ({ description, icon, onClick, title }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`${stylesCss.nodeWorkflow} ${isHovered ? stylesCss.hovered : ''}`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}>
      <Group gap='sm' align='center' wrap='nowrap'>
        <img src={icon} alt={title} className={stylesCss.nodeIcon} />
        <Stack gap={0} className={stylesCss.nodeContent}>
          <KanbanText size='md' fw={500} className={stylesCss.nodeTitle}>
            {title}
          </KanbanText>
          <KanbanText size='sm' c='dimmed' className={stylesCss.nodeDescription}>
            {description}
          </KanbanText>
        </Stack>
      </Group>
    </div>
  );
};

export const WorkflowRightSideBar: React.FC<WorkflowRightSideBarProps> = ({
  changeId,
  listNodeOpened,
  nodes,
  onNodeCreate,
  onNodeSave,
  onSettingsClose,
  selectedNode,
  setListNodeOpened,
  type,
  workflowId,
}) => {
  const canEditChange = useCheckPermissons([changePermissionConfigs.edit]);
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const isViewMode = actionParam === EntityAction.VIEW;
  const [searchQuery, setSearchQuery] = useState('');
  const handleOpenPanel = () => {
    setListNodeOpened(!listNodeOpened);
  };

  const { data: listSettingNode } = useFetch(ChangeWorkflowNodeApi.findAll(), {
    enabled: !!listNodeOpened,
  });

  const { data: checkListData } = useFetch(ChangeRequestApi.findAllWorkflows(changeId, ChangeWorkflowTypeEnum.CHECKLIST), {
    enabled: !!changeId && !!listNodeOpened && ChangeWorkflowTypeEnum.IMPLEMENTATION === type,
  });
  const handleNodeClick = (node: ChangeWorkflowNode) => {
    const nodeId = node.nodeId !== START_NODE_ID ? `node-${node.id}-${Date.now()}` : START_NODE_ID;
    node.nodeId = nodeId;
    onNodeCreate?.(node);
    setListNodeOpened(false);
  };

  /**
   * get nodes in siderightbar.
   */
  // K-tool gencode
  const getNodes = useCallback(
    (search: string) => {
      const listNodes =
        listSettingNode?.data?.map((node) => ({
          ...node,
          type: ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE,
        })) || [];
      let nodeCp: ChangeWorkflowNode[] = listNodes;
      let checklistNodes: ChangeWorkflowNode[] = [];
      if (ChangeWorkflowTypeEnum.IMPLEMENTATION === type && listNodeOpened) {
        checklistNodes =
          checkListData?.data?.map((obj) => {
            return {
              id: obj.id || 0,
              nodeId: obj.id?.toString(),
              nodeName: obj.name || '',
              type: ChangeRequestWorkflowNodeTypeEnum.CHECKLIST_NODE,
              createdDate: obj.createdDate,
              createdBy: obj.createdBy,
              modifiedBy: obj.modifiedBy,
              modifiedDate: obj.modifiedDate,
              childrenData: obj.workflowData,
            };
          }) || [];
        nodeCp = [...checklistNodes, ...listNodes];

        // Add start node if missing
        if (!nodes.some((node) => node.type === ChangeRequestWorkflowNodeTypeEnum.START_NODE)) {
          const startNode: ChangeWorkflowNode = {
            id: 0,
            nodeId: START_NODE_ID,
            nodeName: START_NODE_LABLE,
            type: ChangeRequestWorkflowNodeTypeEnum.START_NODE,
          };
          nodeCp.unshift(startNode);
        } else {
          nodeCp = [...checklistNodes, ...listNodes];
        }
        // Add end node if missing
        // You may need to import END_NODE_ID and END_NODE_LABEL from your constants file
        if (!nodes.some((node) => node.type === ChangeRequestWorkflowNodeTypeEnum.END_NODE)) {
          const startNode: ChangeWorkflowNode = {
            id: 0,
            nodeId: END_NODE_ID,
            nodeName: END_NODE_LABLE,
            type: ChangeRequestWorkflowNodeTypeEnum.END_NODE,
          };
          nodeCp.unshift(startNode);
        }
      }

      return nodeCp.filter((obj) => obj.nodeName.toLocaleLowerCase().includes(search.toLocaleLowerCase()));
    },
    [listSettingNode?.data, type, checkListData?.data, listNodeOpened, nodes],
  );

  /**
   * view rightsidebar setting note
   */
  if (selectedNode && selectedNode.type === ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE) {
    return (
      <NodeSettingsPanel workflowId={workflowId} changeId={changeId} node={selectedNode} onClose={() => onSettingsClose?.()} onSave={onNodeSave} />
    );
  }

  const filteredNodes = getNodes(searchQuery);

  return (
    <>
      {canEditChange && !isViewMode && (
        <Flex className={stylesCss.addButtonContainer}>
          <KanbanIconButton size='lg' variant='outline' onClick={handleOpenPanel}>
            <IconPlus />
          </KanbanIconButton>
        </Flex>
      )}

      <Transition mounted={listNodeOpened && canEditChange} transition='slide-left' duration={300} timingFunction='ease'>
        {(styles) => (
          <Paper className={stylesCss.sidePanel} style={styles}>
            <Flex justify='space-between' align='center' className={stylesCss.panelHeader}>
              <KanbanText size='xl' fw={700}>
                What happens next ?
              </KanbanText>
              <KanbanIconButton onClick={() => setListNodeOpened(false)} variant='subtle' size='sm'>
                <IconX size={18} />
              </KanbanIconButton>
            </Flex>

            <Stack gap='md' className={stylesCss.panelContent}>
              <KanbanInput
                placeholder='Node Name'
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                maxLength={COMMON_MAX_LENGTH}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={stylesCss.searchInput}
              />
              {filteredNodes.length > 0 ? (
                filteredNodes.map((node, index) => (
                  <NodeWorkflow
                    key={index}
                    title={node.nodeName}
                    description={node.description || ''}
                    icon={getNodeIcon(node)}
                    onClick={() => handleNodeClick(node)}
                  />
                ))
              ) : (
                <KanbanText size='sm' c='dimmed' ta='center' py='xl' className={stylesCss.noResults}>
                  No nodes found matching
                </KanbanText>
              )}
            </Stack>
          </Paper>
        )}
      </Transition>
    </>
  );
};
