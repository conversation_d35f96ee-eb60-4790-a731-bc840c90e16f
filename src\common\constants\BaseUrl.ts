import { getConfigs } from '@core/configs/Configs';

const config = getConfigs();

export const DEFAULT_VERSION = 'v1';
export const RTS_URL = `${config.buildApiBaseUrl('rts')}`;
export const SERVER_URL = `${config.buildApiBaseUrl('server')}`;
export const BaseURL = {
  //Server API
  user: `${SERVER_URL}/${DEFAULT_VERSION}/systems/users`,
  group: `${SERVER_URL}/${DEFAULT_VERSION}/systems/groups`,
  role: `${SERVER_URL}/${DEFAULT_VERSION}/systems/roles`,
  mail: `${SERVER_URL}/${DEFAULT_VERSION}/mail`,
  customField: `${SERVER_URL}/${DEFAULT_VERSION}/systems/custom-fields`,
  changeTemplate: `${SERVER_URL}/${DEFAULT_VERSION}/systems/change-templates`,
  changeStatus: `${SERVER_URL}/${DEFAULT_VERSION}/systems/change-status`,
  changeDocument: `${SERVER_URL}/${DEFAULT_VERSION}/systems/change-documents`,
  workflowNodes: `${SERVER_URL}/${DEFAULT_VERSION}/systems/change-workflow-nodes`,
  changeflowNodes: `${SERVER_URL}/${DEFAULT_VERSION}/systems/change-flow-nodes`,
  changeNode: `${SERVER_URL}/${DEFAULT_VERSION}/systems/change-nodes`,
  changeRequest: `${SERVER_URL}/${DEFAULT_VERSION}/change-requests`,
  changeRequestDocuments: (changeRequestId: number) => `${SERVER_URL}/${DEFAULT_VERSION}/change-requests/${changeRequestId}/documents`,
  changeRequestReview: (changeRequestId: number) => `${SERVER_URL}/${DEFAULT_VERSION}/change-requests/${changeRequestId}/reviews`,
  changeFlow: `${SERVER_URL}/${DEFAULT_VERSION}/systems/change-flows`,
  changeRequestApproval: `${SERVER_URL}/${DEFAULT_VERSION}/change-request-approvals`,

  // Integration
  itCmdb: `${SERVER_URL}/${DEFAULT_VERSION}/integration/itcmdb`,
  integrationJenkins: `${SERVER_URL}/${DEFAULT_VERSION}/integration/jenkins`,
  integrationWla: `${SERVER_URL}/${DEFAULT_VERSION}/integration/wla`,

  //Notification
  notificationEmailTemplate: `${SERVER_URL}/${DEFAULT_VERSION}/systems/email-templates`,

  //User preferences
  userPreference: `${SERVER_URL}/${DEFAULT_VERSION}/user-preferences`,
};
