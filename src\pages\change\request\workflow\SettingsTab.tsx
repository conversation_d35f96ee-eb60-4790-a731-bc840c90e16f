import { ChangeRequestApi } from '@api/ChangeRequestApi';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { Stack } from '@mantine/core';
import { getDefaultTableAffected, KanbanCheckbox, KanbanTextarea } from 'kanban-design-system';
import React, { useCallback, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { CustomNodeData } from '@pages/change/request/workflow/reactFlow/CustomNode';
import { SelectWithPage } from '@components/SelectWithPage';
import { initOrUpdatedFilterPayloads, UserPageFilter } from '@pages/configuration/user/users';
import { UserApi } from '@api/UserApi';
import CiInformation from './CiInformation';
import { COMMON_DESCRIPTION_MAX_LENGTH } from '@common/constants/ValidationConstant';

type SettingsTabProps = {
  customNodeData: CustomNodeData | undefined;
  setCustomNodeData: (data: React.SetStateAction<CustomNodeData | undefined>) => void;
};

export const SettingsTab: React.FC<SettingsTabProps> = ({ customNodeData, setCustomNodeData }) => {
  const changeId = Number(useParams().changeId);
  const [inputFilters, setInputFilters] = useState<UserPageFilter>({});
  const {
    fetchNextPage: fetchNextPageUsers,
    flatData: usersData,
    isFetching: isCabsFetching,
  } = useInfiniteFetch(ChangeRequestApi.findAllUsersCab(initOrUpdatedFilterPayloads(getDefaultTableAffected(), inputFilters), changeId), {
    showLoading: false,
  });

  const [inputAssignFilters, setInputAssignFilters] = useState<UserPageFilter>({});
  const {
    fetchNextPage: fetchAssignNextPageUsers,
    flatData: usersAssignData,
    isFetching: isAssignFetching,
  } = useInfiniteFetch(UserApi.findAll(initOrUpdatedFilterPayloads(getDefaultTableAffected(), inputAssignFilters)), {
    showLoading: false,
  });

  const usersComboxOptions = useMemo(() => {
    return usersData.map((obj) => ({ value: `${obj.userName}`, label: obj.userName }));
  }, [usersData]);

  const usersAssignComboxOptions = useMemo(() => {
    return usersAssignData.map((obj) => ({ value: `${obj.userName}`, label: obj.userName }));
  }, [usersAssignData]);

  const handleInputFilterChange = useCallback(
    (key: keyof typeof inputFilters, value: string) => {
      const updatedFilters = { ...inputFilters, [key]: value };
      setInputFilters(updatedFilters);
    },
    [inputFilters],
  );

  const handleAssignInputFilterChange = useCallback(
    (key: keyof typeof inputAssignFilters, value: string) => {
      const updatedFilters = { ...inputAssignFilters, [key]: value };
      setInputAssignFilters(updatedFilters);
    },
    [inputAssignFilters],
  );

  return (
    <Stack gap='md'>
      <SelectWithPage
        options={usersAssignComboxOptions}
        onChange={(data) => {
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              settings: {
                ...prev.settings,
                assign: data,
              },
            };
          });
        }}
        required
        label='Assign'
        onSearch={(val) => handleAssignInputFilterChange('filterUsername', val || '')}
        handleScrollToBottom={fetchAssignNextPageUsers}
        value={
          [
            ...usersAssignComboxOptions,
            {
              value: customNodeData?.settings?.assign || '',
              label: customNodeData?.settings?.assign || '',
            },
          ].filter((e) => e.value === customNodeData?.settings?.assign)[0] || ''
        }
        isLoading={isAssignFetching}
        onBlur={() => handleAssignInputFilterChange('filterUsername', '')}
      />

      <SelectWithPage
        options={usersComboxOptions}
        onChange={(data) => {
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              settings: {
                ...prev.settings,
                userCab: data,
              },
            };
          });
        }}
        required
        label='User CAB'
        onSearch={(val) => handleInputFilterChange('filterUsername', val || '')}
        handleScrollToBottom={fetchNextPageUsers}
        value={
          [
            ...usersComboxOptions,
            {
              value: customNodeData?.settings?.userCab || '',
              label: customNodeData?.settings?.userCab || '',
            },
          ].filter((e) => e.value === customNodeData?.settings?.userCab)[0] || undefined
        }
        isLoading={isCabsFetching}
        onBlur={() => handleInputFilterChange('filterUsername', '')}
      />

      <KanbanCheckbox
        label='Mock status node'
        checked={customNodeData?.settings?.success}
        onChange={(event) =>
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              settings: {
                ...prev.settings,
                success: event.currentTarget.checked,
              },
            };
          })
        }
      />

      <CiInformation customNodeData={customNodeData} setCustomNodeData={setCustomNodeData} />
      <KanbanTextarea
        label='Service Affected Comment'
        value={customNodeData?.settings?.comment}
        maxLength={COMMON_DESCRIPTION_MAX_LENGTH}
        minRows={4}
        onChange={(event) => {
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              settings: {
                ...prev.settings,
                comment: event.currentTarget.value,
              },
            };
          });
        }}
      />
    </Stack>
  );
};

export default SettingsTab;
