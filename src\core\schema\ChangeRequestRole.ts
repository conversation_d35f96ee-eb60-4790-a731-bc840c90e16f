import { z } from 'zod';
import { AuditSchema } from './Common';
import { ChangeRequestRoleUserSchema } from './ChangeRequestRoleUser';
import { ChangeFlowNodeSchema } from '@core/schema/ChangeFlowSchema';
import { ChangeRequestSchema } from './ChangeRequest';

export const ChangeRequestRoleUserListSchema = z.object({
  cabGroupName: z.string().nullish(),
  users: z.array(ChangeRequestRoleUserSchema),
});
export type ChangeRequestRoleUserList = z.infer<typeof ChangeRequestRoleUserListSchema>;

export const ChangeRequestRoleWorkflowListSchema = z.object({
  changeWorkflowId: z.number(),
  changeWorkflowName: z.string().nullish(),
  groups: z.array(ChangeRequestRoleUserListSchema).nullish(),
});

export const ChangeRequestApprovalRoleSchema = z
  .object({
    id: z.number().nullish(),
    changeFlowNodeId: z.number().nullish(),
    changeRequestId: z.number().nullish(),
    changeFlowNode: ChangeFlowNodeSchema.omit({ nodeId: true, groups: true }),
    cabUserGroups: z.array(ChangeRequestRoleUserSchema).nullish(),
    groupedCabUserGroups: z.array(ChangeRequestRoleUserListSchema).nullish(),
    workflows: z.array(ChangeRequestRoleWorkflowListSchema).nullish(),
  })
  .merge(AuditSchema);

export const ChangeRequestApprovalRoleListSchema = z.object({
  changeCoordinator: z.string().nullish(),
  roles: z.array(ChangeRequestApprovalRoleSchema).nullish(),
  activeRoles: z.array(z.string()).nullish(),
  changeRequest: ChangeRequestSchema.pick({ coordinator: true }).nullish(),
});

export type ChangeRequestRoleList = z.infer<typeof ChangeRequestApprovalRoleListSchema>;
export type ChangeRequestRole = z.infer<typeof ChangeRequestApprovalRoleSchema>;
