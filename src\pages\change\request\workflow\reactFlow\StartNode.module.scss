.startNode {
  /* Theme tokens */
  --bg: #ffffff;
  --text: #111827;
  /* gray-900 */
  --muted: #6b7280;
  /* gray-500 */
  --border: #e5e7eb;
  /* gray-200 */
  --shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
  --accent: #9ca3af;
  /* default accent (gray-400) */
  --accent-soft: rgba(156, 163, 175, 0.12);

  position: relative;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 200px;
  min-height: 100px;
  border-radius: 12px;
  border: 1px solid var(--border);
  background: var(--bg);
  box-shadow: var(--shadow);
  padding: 8px 12px 10px 12px;
  cursor: pointer;
  transition: transform 120ms ease, box-shadow 120ms ease, border-color 120ms ease, background 120ms ease;
  user-select: none;
  overflow: hidden;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);

    .iconWrap {
      transform: translateZ(0) scale(1.04);
    }
  }

  /* Left accent bar like n8n */
  &::before {
    content: "";
    position: absolute;
    inset: 0 auto 0 0;
    width: 4px;
    background: var(--accent);
  }

  /* Selected state -> orange tone */
  &.selected {
    --accent: #ff6d5a;
    --accent-soft: rgba(255, 109, 90, 0.12);
    border-color: #ffb5ab;
    box-shadow: 0 0 0 2px rgba(255, 109, 90, 0.18), var(--shadow);

    .title {
      color: #ff6d5a;
    }

    .handle {
      border-color: #ff6d5a;
      box-shadow: 0 0 0 3px rgba(255, 109, 90, 0.15);
    }
  }

  /* Running state -> green tone */
  &.running {
    --accent: #16a34a;
    /* green-600 */
    --accent-soft: rgba(22, 163, 74, 0.12);
    border-color: #34d399;
    /* green-400 */

    .iconWrap,
    .statusDot {
      animation: nodePulse 2s ease-in-out infinite;
    }

    .title {
      color: #16a34a;
    }
  }
}

/* Header: icon + title (n8n-like) */
.header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 2px 0 2px;
}

.iconWrap {
  width: 28px;
  height: 28px;
  border-radius: 8px;
  border: 1px solid var(--border);
  background: #fff;
  display: grid;
  place-items: center;
  transition: transform 120ms ease, border-color 120ms ease, background 120ms ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);

  /* subtle accent halo */
  position: relative;
}

.iconWrap::after {
  content: "";
  position: absolute;
  inset: -2px;
  border-radius: 10px;
  background: var(--accent-soft);
  pointer-events: none;
}

.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--accent);
  transition: transform 120ms ease;
}

.title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text);
  line-height: 1;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Body / status row */
.body {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 2px 2px 0 2px;
}

.status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  min-height: 18px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 999px;
  background: var(--accent);
  box-shadow: 0 0 0 3px var(--accent-soft);
}

.statusText {
  font-size: 10px;
  font-weight: 500;
  color: var(--accent);
}

.subtleHint {
  font-size: 10px;
  color: #6b7280;
}

/* React Flow handles (ports) */
.handle {
  width: 14px;
  height: 14px;
  background: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 120ms ease, border-color 120ms ease, box-shadow 120ms ease, background 120ms ease;
  cursor: crosshair;
  z-index: 10;

  &:hover {
    transform: translateY(-50%) scale(1.18);
    border-color: var(--accent);
    box-shadow: 0 0 0 4px var(--accent-soft);
  }
}

.handleIn {
  left: -7px;
}

.handleOut {
  right: -7px;
}

/* Running-specific animation for handles/icon if you want pulse */
.runningHandle {
  animation: handlePulse 2s ease-in-out infinite;
}

.runningIcon {
  animation: nodePulse 2s ease-in-out infinite;
}

/* Divider (optional, if needed later) */
.divider {
  height: 1px;
  background: #f3f4f6;
  margin: 4px 0 2px;
}