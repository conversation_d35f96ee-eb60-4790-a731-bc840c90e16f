/* Reviewed by: K-tool gencode */
import { z } from 'zod';
import { WorkflowNodeStatusEnum } from './WorkflowNodeStatusEnum';
/* Created by: K-tool gencode */

export const ChangeRequestWorkflowDetailExecuteSchema = z.object({
  id: z.number().optional(),
  executionId: z.number(),
  nodeId: z.string(),
  nodeName: z.string().nullish(),
  parameterData: z.string(),
  settingData: z.string(),
  error: z.string().nullish(),
  startTime: z.string().nullish(),
  endTime: z.string().nullish(),
  status: z.nativeEnum(WorkflowNodeStatusEnum),
});

export type ChangeRequestWorkflowDetailExecute = z.infer<typeof ChangeRequestWorkflowDetailExecuteSchema>;
