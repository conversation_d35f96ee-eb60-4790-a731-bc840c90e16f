import { CiImpacted } from '@core/schema/ItCmdb';
import { ColumnType, KanbanTable, KanbanTableProps } from 'kanban-design-system';
import React, { useMemo } from 'react';

const columns: ColumnType<CiImpacted>[] = [
  {
    name: 'ciImpactedId',
    title: 'ID',
  },
  {
    name: 'ciImpactedName',
    title: 'Name',
  },
  {
    name: 'ciTypeImpactedName',
    title: 'CI Type',
  },
  {
    name: 'ciChangeDescription',
    title: 'Description',
  },
  {
    name: 'ciChangeName',
    title: 'CI in change',
  },
];

type ExtendiInformationProps = {
  data: CiImpacted[];
};

const ExtendCiInformation: React.FC<ExtendiInformationProps> = ({ data }) => {
  const tableProps: KanbanTableProps<CiImpacted> = useMemo(() => {
    const tblProps: KanbanTableProps<CiImpacted> = {
      columns: columns,
      data: data,
      pagination: {
        enable: false,
      },
    };
    return tblProps;
  }, [data]);
  return <KanbanTable {...tableProps} title='' />;
};

export default ExtendCiInformation;
