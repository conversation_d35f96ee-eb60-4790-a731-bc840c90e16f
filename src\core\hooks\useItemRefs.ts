import { useCallback, useRef } from 'react';

export function useItemRefs<T extends HTMLElement>() {
  const mapRef = useRef(new Map<string, T | null>());
  const waitersRef = useRef(new Map<string, Array<(el: T) => void>>());

  const setRef = useCallback(
    (id: string) => (el: T | null) => {
      mapRef.current.set(id, el);
      if (el && waitersRef.current.has(id)) {
        const current = waitersRef.current.get(id);
        if (current) {
          for (const resolve of current) {
            resolve(el);
          }
        }
        waitersRef.current.delete(id);
      }
    },
    [],
  );
  const removeWaiting = useCallback((id: string, resolveFn?: (value: T | PromiseLike<T>) => void) => {
    if (!resolveFn) {
      return;
    }
    const current = waitersRef.current.get(id);
    if (!current || !current.length) {
      return;
    }
    const arr = current.filter((x) => x !== resolveFn);
    waitersRef.current.set(id, arr);
  }, []);
  const whenReady = useCallback(
    (id: string) => {
      const { promise, reject, resolve } = Promise.withResolvers<T>();

      const el = mapRef.current.get(id);
      if (el) {
        resolve(el);
      } else {
        const arr = waitersRef.current.get(id) ?? [];
        arr.push(resolve);
        waitersRef.current.set(id, arr);
      }

      return {
        promise,
        cancel: () => {
          removeWaiting(id, resolve);
          reject('Cancel');
        },
      };
    },
    [removeWaiting],
  );

  return { refs: mapRef, setRef, whenReady };
}
