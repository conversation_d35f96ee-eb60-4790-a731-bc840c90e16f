import React, { useEffect, useMemo, useState } from 'react';
import { KanbanAccordion, KanbanAccordionData, KanbanText } from 'kanban-design-system';
import { Scroll<PERSON>rea, Stack } from '@mantine/core';
import { AccordionPanelView } from './AccordionPanelView';
import {
  ChangeRequestDocumentActionStatusType,
  ChangeRequestSendToApproverRequest,
  ChangeRequestSendToOwnerRequest,
} from '@models/ChangeRequestDocumentGroupModel';
import { ChangeRequestDocumentSelection, ChangeRequestOwnerSelection } from '../DocumentGroupViewPage';
import { useDocumentContext } from '@context/DocumentPermissionContext';
import { useItemRefs } from '@core/hooks/useItemRefs';
import { DebounceTime } from '@common/constants/DebounceTimeConstant';

interface Props {
  changeRequestId: number;
  selectedOwners: ChangeRequestSendToOwnerRequest[];
  selectedDocuments: ChangeRequestSendToApproverRequest[];
  onToggleOwner: (ownerSelection: ChangeRequestOwnerSelection) => void;
  onToggleDocument: (documentSelection: ChangeRequestDocumentSelection) => void;
  handleActionApproveOrReject: (
    action: ChangeRequestDocumentActionStatusType,
    isActionAll: boolean,
    docId?: number,
    documentApproverLevel?: number,
  ) => void;
}

const DocumentAccordionView: React.FC<Props> = ({
  changeRequestId,
  handleActionApproveOrReject,
  onToggleDocument,
  onToggleOwner,
  selectedDocuments,
  selectedOwners,
}) => {
  const { setRef, whenReady } = useItemRefs<HTMLDivElement>();
  const { documentGroups, focusAccordionId, openedItems, setOpenedItems } = useDocumentContext();
  const [transition, setTransition] = useState(0);
  useEffect(() => {
    if (!focusAccordionId) {
      return;
    }

    if (!openedItems.includes(focusAccordionId)) {
      setOpenedItems([...openedItems, focusAccordionId]);
    }

    const { cancel, promise } = whenReady(focusAccordionId);

    promise.then((el) => {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
      el.focus();
    });

    return () => {
      cancel();
    };
  }, [focusAccordionId, openedItems, setOpenedItems, whenReady]);

  const docsAccordionData = useMemo<KanbanAccordionData[]>(() => {
    return documentGroups.map((dg) => ({
      key: dg.id.toString(),
      title: (
        <KanbanText ref={setRef(dg.id.toString())} fw={500}>
          {dg.name}
        </KanbanText>
      ),
      content: (
        <ScrollArea>
          <AccordionPanelView
            key={dg.id}
            changeRequestId={changeRequestId}
            documentGroup={dg}
            selectedOwners={selectedOwners}
            selectedDocuments={selectedDocuments}
            onToggleOwner={onToggleOwner}
            onToggleDocument={onToggleDocument}
            handleActionApproveOrReject={handleActionApproveOrReject}
          />
        </ScrollArea>
      ),
    }));
  }, [changeRequestId, documentGroups, handleActionApproveOrReject, onToggleDocument, onToggleOwner, selectedDocuments, selectedOwners, setRef]);

  return (
    <Stack>
      <KanbanAccordion
        multiple
        chevronPosition='left'
        value={openedItems}
        transitionDuration={transition}
        onChange={(value) => {
          setOpenedItems(Array.isArray(value) ? value : []);
          if (!transition) {
            setTransition(DebounceTime.MILLISECOND_200);
          }
        }}
        data={docsAccordionData}
      />
    </Stack>
  );
};

export default DocumentAccordionView;
