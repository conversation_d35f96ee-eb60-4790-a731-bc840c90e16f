import React from 'react';
import { <PERSON><PERSON>, Divider, Stack } from '@mantine/core';
import { KanbanText, KanbanTextarea } from 'kanban-design-system';
import { IconAlertTriangle } from '@tabler/icons-react';
import { ChangeRequestDocumentActionStatusType, ChangeRequestDocumentApproverRejectRequest } from '@models/ChangeRequestDocumentGroupModel';
import { Control, Controller } from 'react-hook-form';
import { TextLengthConstants } from '@common/constants/TextLengthConstants';

const ACTION_CONFIG: Record<Extract<ChangeRequestDocumentActionStatusType, 'APPROVED' | 'REJECTED'>, { color: string; text: string }> = {
  APPROVED: { color: 'green.7', text: 'approve' },
  REJECTED: { color: 'red.7', text: 'reject' },
};

interface Props {
  action?: ChangeRequestDocumentActionStatusType | null;
  control: Control<ChangeRequestDocumentApproverRejectRequest>;
}

const CommentFormModal = ({ action, control }: Props) => {
  const actionInfo = action && ACTION_CONFIG[action as keyof typeof ACTION_CONFIG];

  return (
    <Stack>
      {actionInfo && (
        <Alert mb='md' radius='sm' variant='light' color='yellow' icon={<IconAlertTriangle size={16} />}>
          <KanbanText fz='sm'>
            Confirming means you{' '}
            <KanbanText span fw={700} c={actionInfo.color}>
              {actionInfo.text}
            </KanbanText>{' '}
            all related items: change details, documents, and workflow
          </KanbanText>
        </Alert>
      )}
      <Controller
        name={'comment'}
        control={control}
        render={({ field, fieldState }) => (
          <KanbanTextarea
            {...field}
            withAsterisk
            label='Comment'
            maxLength={TextLengthConstants.MAX_LENGTH_1000}
            autosize
            minRows={6}
            maxRows={12}
            error={fieldState.error?.message}></KanbanTextarea>
        )}
      />
      <Divider my='md' />
    </Stack>
  );
};

export default CommentFormModal;
