import React from 'react';
import { CustomNodeData } from '@pages/change/request/workflow/reactFlow/CustomNode';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import WlaParametersTab from './WlaParametersTab';
import JenkinsParametersTab from './JenkinsParametersTab';

type ParametersTabProps = {
  customNodeData: CustomNodeData | undefined;
  setCustomNodeData: (data: React.SetStateAction<CustomNodeData | undefined>) => void;
};

const ParametersTab: React.FC<ParametersTabProps> = ({ customNodeData, setCustomNodeData }) => {
  if (customNodeData?.application === ChangeApplicationTypeEnum.JENKINS) {
    return <JenkinsParametersTab customNodeData={customNodeData} setCustomNodeData={setCustomNodeData} />;
  }

  if (customNodeData?.application === ChangeApplicationTypeEnum.WLA) {
    return <WlaParametersTab customNodeData={customNodeData} setCustomNodeData={setCustomNodeData} />;
  }

  return <div>Unsupported application type.</div>;
};

export default ParametersTab;
