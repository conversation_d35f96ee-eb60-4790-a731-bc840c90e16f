{"name": "mbamt-frontend", "version": "0.1.0", "private": true, "dependencies": {"@codemirror/lang-sql": "^6.8.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@edifice-tiptap-extensions/extension-line-height": "^1.5.23", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hello-pangea/dnd": "^16.5.0", "@hookform/resolvers": "^3.10.0", "@mantine/core": "^7.17.2", "@mantine/dates": "^7.17.2", "@mantine/dropzone": "^7.17.2", "@mantine/form": "^7.17.2", "@mantine/hooks": "^7.17.2", "@mantine/notifications": "^7.17.2", "@mantine/spotlight": "^7.17.2", "@mantine/tiptap": "^7.17.4", "@react-keycloak/web": "^3.4.0", "@react-querybuilder/dnd": "^7.7.0", "@react-querybuilder/mantine": "^7.7.0", "@reduxjs/toolkit": "^1.9.7", "@stomp/stompjs": "^7.1.1", "@tabler/icons-react": "^2.42.0", "@tanstack/react-query": "^5.51.21", "@tanstack/react-query-devtools": "^5.74.3", "@tanstack/react-virtual": "^3.11.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tiptap/extension-character-count": "^2.22.3", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-mention": "^2.22.3", "@tiptap/extension-subscript": "^2.11.7", "@tiptap/extension-superscript": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tiptap/suggestion": "^2.22.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.64", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@types/sockjs-client": "^1.5.4", "@uiw/react-codemirror": "^4.23.7", "@xyflow/react": "^12.6.4", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "axios": "^1.7.9", "codemirror": "^6.0.1", "craco-alias": "^2.1.1", "craco-babel-loader": "^1.0.4", "curl-generator": "^0.4.1", "dayjs": "^1.11.10", "dompurify": "^3.1.7", "email-validator": "^2.0.4", "esbuild": "^0.19.4", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^4.3.2", "file-saver": "^2.0.5", "immer": "^10.0.3", "kanban-design-system": "^1.4.46", "keyboard-key": "^1.1.0", "keycloak-js": "^23.0.0", "klona": "^2.0.6", "localforage": "^1.10.0", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "match-sorter": "^6.3.1", "path-to-regexp": "^6.2.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.53.2", "react-imask": "^7.6.1", "react-popper": "^2.3.0", "react-querybuilder": "^7.7.0", "react-redux": "^8.1.3", "react-resizable": "^3.0.5", "react-resizable-panels": "^3.0.4", "react-router-dom": "^6.20.0", "reactflow": "^11.11.4", "redux": "^4.2.1", "redux-saga": "^1.2.3", "sass": "^1.69.5", "sockjs-client": "^1.6.1", "sort-by": "^1.2.0", "styled-components": "^6.1.1", "tailwindcss": "^3.3.5", "tippy.js": "^6.3.7", "typescript": "^5.6.3", "use-undoable": "^5.0.0", "web-vitals": "^2.1.4", "webpack-retry-chunk-load-plugin": "^3.1.1", "zod": "^3.24.2"}, "overrides": {"send": "^0.19.1", "webpack": "^5.95.0", "express": "^4.21.0"}, "cracoConfig": "craco.dev.config.js", "scripts": {"start": "env-cmd -f .env.local cross-env PORT=8001 BROWSER=none EXTEND_ESLINT=true rsbuild dev", "start:fast": "env-cmd -f .env.local cross-env PORT=8001 BROWSER=none DISABLE_ESLINT_PLUGIN=true DISABLE_TYPE_CHECK=true EXTEND_ESLINT=false rsbuild dev", "build": "env-cmd -f .env.uat rsbuild build", "build-tanzu-uat": "env-cmd -f .env.uat rsbuild build", "build-tanzu-pre-release": "env-cmd -f .env.pre-release rsbuild build", "build-tanzu-prod": "env-cmd -f .env.prod rsbuild build", "lint": "eslint --ext .js,.jsx,.ts,.tsx .", "lint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx  .", "eslint": "eslint --ext .js,.jsx,.ts,.tsx .", "eslint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx  .", "test": "craco test", "prepare": "husky"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@rsbuild/core": "^1.2.16", "@rsbuild/plugin-eslint": "^1.1.1", "@rsbuild/plugin-react": "^1.1.1", "@rsbuild/plugin-sass": "^1.2.2", "@rsbuild/plugin-svgr": "^1.0.7", "@rsbuild/plugin-type-check": "^1.2.1", "@types/dompurify": "^3.0.5", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/lodash.debounce": "^4.0.9", "@types/react-grid-layout": "^1.3.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@welldone-software/why-did-you-render": "^8.0.1", "compression-webpack-plugin": "^10.0.0", "craco-esbuild": "^0.5.2", "cross-env": "^7.0.3", "env-cmd": "^10.1.0", "eslint": "^8.35.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-babel-module": "^5.3.1", "eslint-plugin-cypress": "2.11.2", "eslint-plugin-filenames-simple": "^0.9.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-sort-destructure-keys": "^1.3.5", "eslint-plugin-unused-imports": "^3.0.0", "husky": "^9.0.11", "postcss": "^8.4.31", "postcss-preset-mantine": "^1.11.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.2.5", "webpack-merge": "^5.8.0", "workbox-webpack-plugin": "^6.5.3"}}