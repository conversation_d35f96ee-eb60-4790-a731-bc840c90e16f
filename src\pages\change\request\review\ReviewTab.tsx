import React, { useState, useEffect } from 'react';
import { validateBeforeSave, validateSentToApprover, validateSentToOwner } from './validation/ReviewValidationUtils';
import { ChangeRequestReviewStatusEnum, statusOptions } from '@common/constants/ChangeRequestReviewStatusConstants';
import { ChangeRequestReviewApprovalStatusType } from '@common/constants/ChangeRequestReviewApprovalStatusConstants';
import { Group, Paper, Stack, Table, Box, Anchor } from '@mantine/core';
import { IconFile } from '@tabler/icons-react';
import { KanbanButton, KanbanTitle, KanbanTextarea } from 'kanban-design-system';
import { SelectWithPage } from '@components/SelectWithPage';
import ComboboxLoadMore from '@components/ComboboxLoadMore';
import { getDefaultTableAffected, TableAffactedSafeType } from 'kanban-design-system';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import useFetch from '@core/hooks/useFetch';
import { UserApi } from '@api/UserApi';
import { initOrUpdatedFilterPayloads, UserPageFilter } from '@pages/configuration/user/users';
import { ChangeRequestReviewApi } from '@api/ChangeRequestReviewApi';
import { ReviewFormValues } from './ReviewFormValues';
import useMutate from '@core/hooks/useMutate';
import { ACCEPTED_FILE_TYPES } from '@common/constants/ChangeRequestConstants';
import { COMMENT_MAX_LENGTH, NOTE_MAX_LENGTH } from '@common/constants/ValidationConstant';
import classes from '@pages/change/request/review/ReviewTable.module.scss';
import reviewClasses from './ReviewTab.module.scss';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { ApprovalModal } from './components/ApprovalModal';
import { useApprovalModal } from './hooks/ApprovalModal';
import { getApproverPillProps, reviewStatusBgMap } from './constants/ReviewConstants';
import { ApproverComboboxData } from './types/ReviewTypes';
import saveAs from 'file-saver';
import { useSearchParams } from 'react-router-dom';

export const ReviewTab: React.FC<{ changeRequestId: number }> = ({ changeRequestId }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const currentUserState = useSelector(getCurrentUser);
  const currentUser = currentUserState.userInfo;
  const currentUsername = currentUser?.userName || '';
  const isAdmin = currentUser?.isAdmin || false;

  // URL-based mode logic
  const action = searchParams.get('action');
  const isEditMode = action === 'EDIT';
  const isViewMode = action === 'VIEW';

  const [ownerFilters, setOwnerFilters] = useState<UserPageFilter>({});
  const [approverFilters, setApproverFilters] = useState<UserPageFilter>({});
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [skipFileUpload, setSkipFileUpload] = useState<boolean>(false);
  const [review, setReview] = useState<ReviewFormValues>({
    owner: '',
    ownerFullName: '',
    approvers: [],
    status: ChangeRequestReviewStatusEnum.Enum.IN_PROGRESS,
    lastestComment: '',
    note: '',
    documentName: '',
    action: null,
  });

  // Modal state management using custom hook
  const { closeModal, handleAccept, handleReject, modalState, updateComment } = useApprovalModal();

  // Fetch change request data to get coordinator info
  const { data: changeRequestData } = useFetch(ChangeRequestApi.findWithId(changeRequestId), {
    showLoading: false,
  });

  const makeParams = (filters: UserPageFilter): TableAffactedSafeType => {
    const baseParams = getDefaultTableAffected();
    // Reset to first page when searching to ensure we get fresh results
    const paramsWithSearch = initOrUpdatedFilterPayloads(baseParams, filters);
    if (filters.filterUsername) {
      paramsWithSearch.page = 0; // Reset to first page when searching
    }
    return paramsWithSearch;
  };

  const ownerParams = makeParams(ownerFilters);
  const approverParams = makeParams(approverFilters);

  const {
    fetchNextPage: ownerFetchMore,
    flatData: ownerUsers,
    isFetching: isOwnerLoading,
  } = useInfiniteFetch(UserApi.findAll(ownerParams), { showLoading: false });

  const {
    fetchNextPage: approverFetchMore,
    flatData: approverUsers,
    isFetching: isApproverLoading,
  } = useInfiniteFetch(UserApi.findAll(approverParams), { showLoading: false });

  // Helper function to format user display name with strikethrough for inactive users
  const formatUserDisplayName = (name: string, userName: string, isActive: boolean): string => {
    // Use Unicode combining long stroke overlay (U+0336) for strikethrough effect on inactive users
    let displayName: string;
    if (isActive) {
      displayName = name;
    } else {
      const strikethroughChar = '\u0336';
      displayName = name
        .split('')
        .map((char) => char + strikethroughChar)
        .join('');
    }
    return `${displayName} (${userName})`;
  };

  // Filter owner users to only include active users for selection
  const activeOwnerUsers = ownerUsers.filter((u) => u.isActive);

  // Owner options for dropdown (only active users) and display (includes inactive for existing assignments)
  // Include both active users for selection and current owner (even if inactive) for display
  const currentOwnerUser = ownerUsers.find((u) => u.userName === review.owner);
  const optionsUsers = currentOwnerUser && !currentOwnerUser.isActive ? [...activeOwnerUsers, currentOwnerUser] : activeOwnerUsers;

  const ownerOptions = optionsUsers.map((u) => ({
    value: u.userName,
    label: formatUserDisplayName(u.name, u.userName, u.isActive),
  }));

  // If owner not found in user list, add it using ownerFullName from API
  // But only if the current owner matches the ownerFullName (not changed by user)
  if (review.owner && !currentOwnerUser) {
    // Find the full name for current owner from the user list or use ownerFullName from API
    const ownerUser = ownerUsers.find((u) => u.userName === review.owner);
    const displayName = ownerUser?.name || review.ownerFullName || review.owner;

    ownerOptions.unshift({
      value: review.owner,
      label: `${displayName} (${review.owner})`,
    });
  }

  // Filter approver users to only include active users for selection
  const activeApproverUsers = approverUsers.filter((u) => u.isActive);

  // Options for ComboboxLoadMore (approvers)
  const approverComboboxOptions = activeApproverUsers.map((u) => ({
    id: u.userName,
    name: u.name || u.userName, // Store the actual full name
    userName: u.userName,
    fullName: u.name, // Keep full name for search functionality
  }));

  function updateReview<K extends keyof ReviewFormValues>(field: K, value: ReviewFormValues[K]) {
    setReview((prev) => ({ ...prev, [field]: value }));
  }

  // Special handler for owner change to update both owner and ownerFullName
  const handleOwnerChange = (newOwner: string) => {
    const selectedUser = ownerUsers.find((u) => u.userName === newOwner);
    setReview((prev) => ({
      ...prev,
      owner: newOwner,
      ownerFullName: selectedUser?.name || '', // Update ownerFullName with selected user's name
    }));
  };

  // Conditional field editing based on review status
  const isStatusInProgress = review.status === ChangeRequestReviewStatusEnum.Enum.IN_PROGRESS;

  const isChangeCoordinator = (() => {
    if (!currentUser || !changeRequestData?.data) {
      return false;
    }
    return isAdmin || currentUsername === changeRequestData.data.coordinator;
  })();

  // Fetch review data from API
  const { data: reviewData, refetch: refetchReviewData } = useFetch(ChangeRequestReviewApi.findChangeRequestReview(changeRequestId), {
    showLoading: false,
  });

  const isOwner = isAdmin || currentUsername === reviewData?.data?.owner;
  const isApprover = review.approvers?.some((a) => a.approver === currentUsername) ?? false;
  const isOwnerFieldEditable = !isViewMode && isChangeCoordinator;
  const isApproverFieldEditable = !isViewMode && isOwner && !isStatusInProgress;
  const isDocumentFieldEditable = !isViewMode && isOwner && !isStatusInProgress;
  const isNoteFieldEditable = !isViewMode && (isChangeCoordinator || isOwner);

  const showEditButton = isViewMode && (isChangeCoordinator || isOwner);
  const showSendToOwnerButton = isChangeCoordinator;
  const showSendToApproverButton = isOwner;
  const showAcceptRejectButtons = isApprover && review.status === ChangeRequestReviewStatusEnum.Enum.SENT_TO_APPROVER;
  const currentUserApprovalStatus = (() => {
    const currentApprover = review.approvers?.find((a) => a.approver === currentUsername);
    return currentApprover?.approvalStatus;
  })();

  const areApprovalButtonsDisabled = currentUserApprovalStatus !== null && currentUserApprovalStatus !== undefined;

  // Action button handlers
  const handleEdit = () => {
    // Update URL parameter to switch to edit mode
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set('action', 'EDIT');
      return newParams;
    });
  };

  const { mutate: saveAsOwner } = useMutate(
    () => {
      const approvers = review.approvers?.map((a) => a.approver?.trim()).filter((a): a is string => Boolean(a)) ?? [];

      const note = review.note?.trim() || '';
      const owner = review.owner?.trim() || '';

      return ChangeRequestReviewApi.saveOrUpdateForOwner(
        changeRequestId,
        {
          approvers: approvers as [string, ...string[]],
          owner,
          note,
          skipFileUpload,
        },
        selectedFile || undefined,
      );
    },
    {
      successNotification: 'Review saved successfully',
      errorNotification: (error) => ({ message: error.message }),
      onSuccess: () => {
        // Switch back to view mode after successful save
        setSearchParams((prev: URLSearchParams) => {
          const newParams = new URLSearchParams(prev);
          newParams.set('action', 'VIEW');
          return newParams;
        });
        setSelectedFile(null);
        setSkipFileUpload(false);
        refetchReviewData();
      },
    },
  );

  const { mutate: saveAsCoordinator } = useMutate(
    () => {
      const owner = review.owner?.trim() || '';
      const note = review.note?.trim() || '';

      return ChangeRequestReviewApi.saveOrUpdateForCoordinator(changeRequestId, {
        owner,
        note,
      });
    },
    {
      successNotification: 'Review saved successfully',
      errorNotification: (error) => ({ message: error.message }),
      onSuccess: () => {
        // Switch back to view mode after successful save
        setSearchParams((prev: URLSearchParams) => {
          const newParams = new URLSearchParams(prev);
          newParams.set('action', 'VIEW');
          return newParams;
        });
        refetchReviewData();
      },
    },
  );

  const handleSave = () => {
    if (review.status === ChangeRequestReviewStatusEnum.Enum.IN_PROGRESS) {
      saveAsCoordinator(undefined);
    } else {
      if (isOwner) {
        saveAsOwner(undefined);
      } else if (isChangeCoordinator) {
        saveAsCoordinator(undefined);
      }
    }
  };

  const handleCancel = () => {
    // Reset form to original values
    if (reviewData?.data) {
      const data = reviewData.data;
      setReview({
        owner: data.owner || '',
        ownerFullName: data.ownerFullName || '',
        approvers: data.approvers || [],
        status: data.status,
        lastestComment: data.comment || '',
        note: data.note || '',
        documentName: data.documentName || '',
        action: null,
      });
    }
    // Switch back to view mode
    setSearchParams((prev: URLSearchParams) => {
      const newParams = new URLSearchParams(prev);
      newParams.set('action', 'VIEW');
      return newParams;
    });
  };

  // Mutation for updating review status
  const { mutate: updateReviewStatus } = useMutate((status: string) => ChangeRequestReviewApi.updateReviewStatus(changeRequestId, { status }), {
    successNotification: 'Review status updated successfully',
    errorNotification: (error) => ({ message: error.message }),
    onSuccess: () => {
      // Refetch review data after successful update
      refetchReviewData();
    },
  });

  // Mutation for approver actions
  const { mutate: updateApproverStatus } = useMutate(
    (data: { approvalStatus: ChangeRequestReviewApprovalStatusType; comment: string }) =>
      ChangeRequestReviewApi.updateApproverStatus(changeRequestId, data),
    {
      successNotification: 'Approver status updated successfully',
      errorNotification: (error) => ({ message: error.message }),
      onSuccess: () => {
        refetchReviewData();
      },
    },
  );

  // Mutation for downloading review document
  const { mutate: downloadDocument } = useMutate<Blob, void>(() => ChangeRequestReviewApi.downloadReviewDocument(changeRequestId), {
    successNotification: 'Document downloaded successfully',
    errorNotification: (error) => ({ message: error.message }),
    onSuccess: (res) => {
      if (res instanceof Blob) {
        const documentName = review.documentName;
        saveAs(res, documentName);
      }
    },
  });

  const handleDownloadDocument = () => {
    downloadDocument();
  };

  const handleSendToOwner = () => {
    updateReviewStatus(ChangeRequestReviewStatusEnum.Enum.SENT_TO_OWNER);
  };

  const handleSendToApprover = () => {
    if (review.approvers && review.approvers.length > 0) {
      updateReviewStatus(ChangeRequestReviewStatusEnum.Enum.SENT_TO_APPROVER);
    }
  };

  const handleApprovalSubmit = () => {
    if (modalState.action) {
      updateApproverStatus({
        approvalStatus: modalState.action,
        comment: modalState.comment.trim(),
      });
    }

    closeModal();
  };

  // Update review when data is loaded
  useEffect(() => {
    if (reviewData?.data) {
      const data = reviewData.data;
      setReview({
        owner: data.owner || '',
        ownerFullName: data.ownerFullName || '',
        approvers: data.approvers || [],
        status: data.status,
        lastestComment: data.comment || '',
        note: data.note || '',
        documentName: data.documentName || '',
        action: null,
      });
      setSkipFileUpload(data.skipFileUpload || false);
    }
  }, [reviewData]);

  return (
    <Stack gap='md'>
      <Paper withBorder p='md'>
        <Group justify='space-between' align='center' mb='sm'>
          <KanbanTitle fz='h6'>Biên bản nghiệm thu</KanbanTitle>
          <Group gap='sm'>
            {/* Edit/Save/Cancel buttons */}
            {showEditButton && (
              <KanbanButton variant='filled' size='xs' onClick={handleEdit}>
                Edit
              </KanbanButton>
            )}
            {isEditMode && (
              <>
                <KanbanButton variant='filled' size='xs' onClick={handleCancel}>
                  Cancel
                </KanbanButton>
                <KanbanButton
                  variant='filled'
                  size='xs'
                  disabled={!validateBeforeSave(review, selectedFile, isOwner, isChangeCoordinator)}
                  onClick={handleSave}>
                  Save
                </KanbanButton>
              </>
            )}

            {/* Status action buttons */}
            {isViewMode && (
              <>
                {showSendToOwnerButton && (
                  <KanbanButton variant='filled' size='xs' onClick={handleSendToOwner} disabled={!validateSentToOwner(review)}>
                    Send to Owner
                  </KanbanButton>
                )}

                {showSendToApproverButton && review.approvers && review.approvers.length > 0 && (
                  <KanbanButton variant='filled' size='xs' onClick={handleSendToApprover} disabled={!validateSentToApprover(review, selectedFile)}>
                    Send to Approver
                  </KanbanButton>
                )}

                {showAcceptRejectButtons && (
                  <>
                    <KanbanButton variant='filled' size='xs' onClick={handleAccept} disabled={areApprovalButtonsDisabled}>
                      Accept
                    </KanbanButton>
                    <KanbanButton variant='filled' size='xs' onClick={handleReject} disabled={areApprovalButtonsDisabled}>
                      Reject
                    </KanbanButton>
                  </>
                )}
              </>
            )}
          </Group>
        </Group>

        <Table striped highlightOnHover withTableBorder withColumnBorders className={classes.table}>
          <Table.Thead>
            <Table.Tr>
              <Table.Th className={`${classes.headerCell} ${classes.ownerColumn}`}>Owner</Table.Th>
              <Table.Th className={`${classes.headerCell} ${classes.documentColumn}`}>Document</Table.Th>
              <Table.Th className={`${classes.headerCell} ${classes.approverColumn}`}>Approver</Table.Th>
              <Table.Th className={`${classes.headerCell} ${classes.statusColumn}`}>Status</Table.Th>
              <Table.Th className={`${classes.headerCell} ${classes.commentColumn}`}>Comment</Table.Th>
            </Table.Tr>
          </Table.Thead>

          <Table.Tbody>
            <Table.Tr>
              {/* Owner */}
              <Table.Td className={`${classes.cell} ${classes.ownerColumn}`}>
                <Box pt='xs'>
                  <SelectWithPage
                    options={ownerOptions}
                    value={ownerOptions.find((option) => option.value === review.owner)}
                    onChange={(val) => handleOwnerChange(val || '')}
                    onSearch={(val) => setOwnerFilters((p: UserPageFilter) => ({ ...p, filterUsername: val }))}
                    onBlur={() => setOwnerFilters((p: UserPageFilter) => ({ ...p, filterUsername: '' }))}
                    handleScrollToBottom={ownerFetchMore}
                    isLoading={isOwnerLoading}
                    disabled={isViewMode || !isOwnerFieldEditable}
                    clearable={isOwnerFieldEditable}
                  />
                </Box>
              </Table.Td>

              {/* Document */}
              <Table.Td className={`${classes.cell} ${classes.documentColumn}`}>
                <Box pt='xs' className={reviewClasses.documentContainer}>
                  {isEditMode ? (
                    <KanbanButton
                      variant='filled'
                      size='xs'
                      disabled={isViewMode || !isDocumentFieldEditable}
                      className={`${reviewClasses.documentButton} ${!isDocumentFieldEditable ? reviewClasses.disabled : reviewClasses.enabled}`}
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = ACCEPTED_FILE_TYPES;
                        input.onchange = async (e: any) => {
                          const file = e.target.files[0];
                          if (file) {
                            updateReview('documentName', file.name);
                            setSelectedFile(file);
                            setSkipFileUpload(false);
                          }
                        };
                        input.click();
                      }}>
                      <span className={reviewClasses.documentButtonText}>
                        {selectedFile ? selectedFile.name : review.documentName || '+ Add a document'}
                      </span>
                    </KanbanButton>
                  ) : review.documentName ? (
                    <Group gap={4}>
                      <IconFile size={14} />
                      <Anchor size='xs' component='button' onClick={handleDownloadDocument}>
                        {review.documentName}
                      </Anchor>
                    </Group>
                  ) : (
                    <Box component='span' c='gray.6'>
                      No document
                    </Box>
                  )}
                </Box>
              </Table.Td>

              {/* Approver */}
              <Table.Td className={`${classes.cell} ${classes.approverColumn}`}>
                <Box pt='xs'>
                  <Box w='100%' className={reviewClasses.approverComboboxLoadMore}>
                    <ComboboxLoadMore<ApproverComboboxData>
                      options={approverComboboxOptions}
                      values={
                        review.approvers
                          ?.filter((a) => Boolean(a.approver?.trim()))
                          .map((approver) => {
                            const username = approver.approver?.trim() || '';

                            // Three-tier fallback hierarchy for full name: API response → cached user list → username
                            const cachedUser = approverUsers.find((u) => u.userName === username);
                            const fullName =
                              approver.fullName?.trim() || // 1. Primary: API response
                              cachedUser?.name?.trim() || // 2. Secondary: cached user list
                              username; // 3. Tertiary: username fallback

                            return {
                              id: username,
                              name: fullName,
                              userName: username,
                              fullName: fullName,
                              approvalStatus: approver.approvalStatus,
                            };
                          }) || []
                      }
                      onChange={(selectedUsers) => {
                        // Convert ComboboxLoadMore values back to approver object structure
                        // Preserve existing approval status for existing approvers
                        const approverObjects = selectedUsers.map((user) => {
                          const existingApprover = review.approvers?.find((a) => a.approver === user.userName);
                          return {
                            approver: user.userName,
                            approvalStatus: existingApprover?.approvalStatus ?? null, // Preserve existing status or null for new approvers
                          };
                        });
                        updateReview('approvers', approverObjects);
                      }}
                      onSearch={(searchValue) => {
                        // Update search filter and reset pagination
                        setApproverFilters((p: UserPageFilter) => ({
                          ...p,
                          filterUsername: searchValue,
                        }));
                      }}
                      onBlur={() => {
                        // Clear search filter when losing focus to show all users again
                        setApproverFilters((p: UserPageFilter) => ({
                          ...p,
                          filterUsername: '',
                        }));
                      }}
                      onScroll={approverFetchMore}
                      renderPillLabel={(user) => {
                        // Find the user in the full user list to check active status
                        const fullUserData = approverUsers.find((u) => u.userName === user.userName);
                        const isActive = fullUserData?.isActive ?? true; // Default to active if not found
                        return formatUserDisplayName(user.name, user.userName, isActive);
                      }}
                      renderPillProps={(user) => getApproverPillProps(user.approvalStatus)}
                      renderOptionLabel={(user) => `${user.name} (${user.userName})`}
                      filter={(searchValue, item) => {
                        const searchLower = searchValue.toLowerCase();
                        return item.userName.toLowerCase().includes(searchLower) || item.name.toLowerCase().includes(searchLower);
                      }}
                      placeholder='Select approvers'
                      clearable={isApproverFieldEditable}
                      disabled={isViewMode || !isApproverFieldEditable}
                      isLoading={isApproverLoading}
                      scrollableForValue
                    />
                  </Box>
                </Box>
              </Table.Td>

              {/* Status */}
              <Table.Td className={`${classes.cell} ${classes.statusColumn}`}>
                <Box pt='xs'>
                  <Box
                    component='span'
                    className={reviewClasses.statusBadge}
                    bg={reviewStatusBgMap[review.status]?.bg || 'gray.1'}
                    c={reviewStatusBgMap[review.status]?.color || 'gray.9'}
                    bd={reviewStatusBgMap[review.status]?.borderColor || '1px solid var(--mantine-color-gray-3)'}>
                    {statusOptions.find((option) => option.value === review.status)?.label || review.status}
                  </Box>
                </Box>
              </Table.Td>

              {/* Comment */}
              <Table.Td className={`${classes.cell} ${classes.commentColumn}`}>
                <Box pt='xs'>
                  <KanbanTextarea
                    value={review.lastestComment || ''}
                    onChange={(e) => updateReview('lastestComment', e.target.value)}
                    placeholder='Comment...'
                    disabled={true}
                    maxLength={COMMENT_MAX_LENGTH}
                    minRows={4}
                  />
                </Box>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>

        {/* Note Section */}
        <Paper withBorder radius='md' p='md' mt='md' className={reviewClasses.noteSection}>
          <Stack gap='sm'>
            <KanbanTitle order={4} mb={0} className={reviewClasses.noteTitle}>
              Note
            </KanbanTitle>
            <Box pt='xs'>
              <KanbanTextarea
                value={review.note || ''}
                onChange={(e) => updateReview('note', e.target.value)}
                placeholder='Add your notes here...'
                disabled={isViewMode || !isNoteFieldEditable}
                maxLength={NOTE_MAX_LENGTH}
                minRows={3}
                className={reviewClasses.noteTextarea}
              />
            </Box>
          </Stack>
        </Paper>
      </Paper>

      {/* Approval/Rejection Modal */}
      <ApprovalModal
        opened={modalState.isOpen}
        onClose={closeModal}
        approvalAction={modalState.action}
        approvalComment={modalState.comment}
        onCommentChange={updateComment}
        onSubmit={handleApprovalSubmit}
      />
    </Stack>
  );
};
