import { ChangeRequestReviewApprovalStatusType } from '@common/constants/ChangeRequestReviewApprovalStatusConstants';

// Custom type for approver data with approval status
export interface ApproverComboboxData {
  id: string;
  name: string;
  userName: string;
  fullName: string;
  approvalStatus?: ChangeRequestReviewApprovalStatusType | null;
}

// Props for the main ReviewTab component
export interface ReviewTabProps {
  changeRequestId: number;
}

// Modal state interface
export interface ApprovalModalState {
  isOpen: boolean;
  action: ChangeRequestReviewApprovalStatusType | null;
  comment: string;
}
