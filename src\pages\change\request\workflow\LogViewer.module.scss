/* LogViewer.module.scss - panel luôn nằm dưới màn hình, width 100%, height 30% viewport */

/* New classes extracted from inline styles in LogViewer.tsx */
.settingsPanel {
  width: 100%;
  height: 300px;
  position: relative;
  display: flex;
  flex-direction: column;
  resize: none;
  overflow: hidden;
}

.header {
  flex-shrink: 0;
}

/* Content area container */
.content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Stages Section */
.stagesSection {
  flex-shrink: 0;
  margin-bottom: 12px;
}

.stageBox {
  /* keep box to allow future layout styling */
}

.stagePaper {
  background: #fff;
  cursor: pointer;
  text-align: center;
  transition: background 0.2s, box-shadow 0.2s;
  border-color: #e9ecef;
}

.stagePaperActive {
  background: #f1f3f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

/* Border color per Jenkins status via data-attribute */
.stagePaper[data-status='SUCCESS'] {
  border-color: green;
}

.stagePaper[data-status='FAILED'] {
  border-color: red;
}

.stagePaper[data-status='PENDING'] {
  border-color: gray;
}

.stagePaper[data-status='IN_PROGRESS'] {
  border-color: blue;
}

.stagePaper[data-status='ABORTED'] {
  border-color: orange;
}

.stagePaper[data-status='SKIPPED'] {
  border-color: gray;
}

.stagePaper[data-status='UNSTABLE'] {
  border-color: yellow;
}

.stagePaper[data-status="PAUSED"] {
  border-color: cyan;
}

.stagePaper[data-status="CANCELLED"] {
  border-color: red;
}

/* Status text color */
.stageStatus {
  font-size: 0.95em;
  font-weight: 500;
  margin-top: 2px;
}

.stageStatus[data-status='SUCCESS'] {
  color: green;
}

.stageStatus[data-status='FAILED'] {
  color: red;
}

.stageStatus[data-status='PENDING'] {
  color: gray;
}

.stageStatus[data-status='IN_PROGRESS'] {
  color: blue;
}

.stageStatus[data-status='ABORTED'] {
  color: orange;
}

.stageStatus[data-status='SKIPPED'] {
  color: gray;
}

.stageStatus[data-status='UNSTABLE'] {
  color: yellow;
}

.stageStatus[data-status='PAUSED'] {
  color: cyan;
}

.stageStatus[data-status='CANCELLED'] {
  color: red;
}

/* Chevron rotation */
.chevron {
  transition: transform 0.2s;
}

.chevronOpen {
  transform: rotate(180deg);
}

/* Console styles */
.console {
  flex: 1;
  background: #181818;
  color: #e9ecef;
  border-radius: 8px;
  font-family: monospace;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Console Header */
.consoleHeader {
  flex-shrink: 0;
  border-bottom: 1px solid #333;
}

.consoleHeaderTitle {
  font-weight: 600;
}

/* Scroll Area */
.scrollArea {
  flex: 1;
}

/* Log preformatted text */
.logPre {
  font-size: 13px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.4;
  min-height: 100%;
}

/* Resize Handle */
.resizeHandle {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 8px;
  cursor: ns-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0 0 8px 8px;
  z-index: 10;
}

.resizeIcon {
  color: #666;
}

/* Keep original legacy classes (unused for now) for backward compatibility */
.logViewerPanelFixed {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 30vh;
  background: #fff;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  padding: 0;
  margin: 0;
  max-width: 100vw;
  display: flex;
  flex-direction: column;
}

.logViewerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px 8px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
}

.logViewerTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: #222;
}

.logViewerStages {
  padding: 16px 24px 24px 24px;
  display: flex;
  gap: 16px;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  height: 100%;
  width: 100%;
}

.logViewerStageBox {
  flex: 1 1 0;
  min-width: 0;
  max-width: none;
  display: flex;
  flex-direction: column;
}

.logViewerStagePaper {
  background: #fff;
  border: 1.5px solid #e9ecef;
  border-radius: 8px;
  padding: 10px 8px 8px 8px;
  text-align: center;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}

.logViewerStagePaperActive {
  background: #f1f3f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

.logViewerStageStatus {
  font-size: 0.95em;
  font-weight: 500;
  margin-top: 2px;
}

.logViewerStageCollapse {
  margin-top: 8px;
  padding: 10px;
  background: #181818;
  color: #e9ecef;
  border-radius: 8px;
  font-family: monospace;
  width: 100vw;
  max-width: 100vw;
  left: 0;
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  min-width: 0;
  z-index: 2;
  display: block;
}

.logViewerStageCollapseTitle {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #e9ecef;
  font-size: 1em;
}