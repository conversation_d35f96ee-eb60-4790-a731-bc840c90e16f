import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { createListSchema, createResponseSchema, ResponseData } from '@core/schema/Common';
import { Attribute, AttributeSchema, CiInformationSchema, CiInformationType, CiType, CiTypeSchema } from '@core/schema/ItCmdb';

export class ItCmdbApi {
  static getCiTypes(): RequestConfig<ResponseData<CiType[]>> {
    return {
      url: `${BaseURL.itCmdb}/ci-types`,
      method: 'GET',
      schema: createResponseSchema(createListSchema(CiTypeSchema)),
    };
  }

  static getAttributes(id: number): RequestConfig<ResponseData<Attribute[]>> {
    return {
      url: `${BaseURL.itCmdb}/ci-types/${id}/attributes`,
      method: 'GET',
      schema: createResponseSchema(createListSchema(AttributeSchema)),
    };
  }

  static getCisInformation(nodeId: number, value: string): RequestConfig<ResponseData<CiInformationType>> {
    return {
      url: `${BaseURL.itCmdb}/cis?nodeId=${nodeId}&value=${value}`,
      method: 'GET',
      schema: createResponseSchema(CiInformationSchema),
    };
  }
}
