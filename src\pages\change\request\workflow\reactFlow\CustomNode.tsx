import { Box, Tooltip } from '@mantine/core';
import { IconAlertTriangle, IconTerminal2 } from '@tabler/icons-react';
import { Handle, Position } from '@xyflow/react';
import React, { useEffect, useState } from 'react';
import stylesCss from './CustomNode.module.scss';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { KanbanText } from 'kanban-design-system';
import clsx from 'clsx';
import { CiInformationType } from '@core/schema/ItCmdb';

export type NodeData = {
  label: string;
  icon: string;
  isRunning?: boolean;
  errors?: string[];
  isCompleted?: boolean;
  changeNodeId?: number;
  originalWidth?: number;
  originalHeight?: number;
};

export type CustomNodeData = NodeData & {
  parameters?: NodeParameterData;
  subtitle?: string;
  settings?: NodeSettingData;
  application: ChangeApplicationTypeEnum;
  content?: string;
  title?: string;
  success?: boolean;
  onResizeStart?: () => void;
  onResizeStop?: () => void;
  draggable?: boolean;
};

export type CustomNodeProps = {
  id: string;
  data: CustomNodeData;
  selected?: boolean;
  onLogClick?: (node: string) => void;
};

export type JenkinsParameter = {
  name: string;
  type: string;
  defaultValue?: unknown;
  description?: string;
  choices?: string[];
};

export type NodeParameterData = {
  operation?: string;
  jobPath?: string;
  jenkinsParams?: JenkinsParameter[];
  jenkinsParamValues?: Record<string, string | number | boolean>;
  //wla
  sysId?: string;
  taskName?: string;
  wlaParams?: Record<string, string>;
};

export type NodeSettingData = {
  userCab?: string;
  cis?: CiInformationType;
  assign?: string;
  success?: boolean;
  comment?: string;
};

const CustomNode = ({ data, id, onLogClick, selected }: CustomNodeProps) => {
  const [isError, setIsError] = useState<boolean>(false);
  const [errorDetails, setErrorDetails] = useState<string>('');
  const iconSrc = data.icon;
  // Hàm xử lý khi click icon log
  const handleLogClick = (e: React.MouseEvent) => {
    if (onLogClick) {
      e.stopPropagation();
      onLogClick(id);
    }
  };

  useEffect(() => {
    const hasError = () => {
      const settings = data.settings;
      const errors: string[] = [];

      // Kiểm tra các cài đặt
      if (!settings?.userCab) {
        errors.push('User CAB is required.');
      }

      if (errors.length > 0) {
        setErrorDetails(errors.join('\n'));
        return true;
      }

      return false;
    };

    setIsError(hasError());
  }, [data, id]);

  // Build các class names với clsx
  const nodeClass = clsx(stylesCss.customNode, selected && stylesCss.selected, data.isRunning && stylesCss.running);

  const statusIndicatorClass = clsx(stylesCss.statusIndicator, {
    [stylesCss.errorStatus]: isError && !data.isRunning,
    [stylesCss.successStatus]: !isError && !data.isRunning,
    [stylesCss.runningStatus]: data.isRunning,
  });

  const iconContainerClass = clsx(stylesCss.iconContainer, data.isRunning && stylesCss.runningIcon);

  const targetHandleClass = clsx(stylesCss.handle, stylesCss.targetHandle, stylesCss.leftHandle);

  const sourceHandleClass = clsx(stylesCss.handle, stylesCss.sourceHandle, stylesCss.rightHandle);

  const leftHintClass = clsx(stylesCss.connectionHint, stylesCss.leftHint);

  const rightHintClass = clsx(stylesCss.connectionHint, stylesCss.rightHint);

  return (
    <Box className={stylesCss.nodeWrapper}>
      <Box className={nodeClass}>
        {/* Running Spinner Ring */}
        {data.isRunning && (
          <Box className={stylesCss.runningRing}>
            <Box className={stylesCss.spinnerRing} />
            <Box className={stylesCss.spinnerRingInner} />
          </Box>
        )}

        {/** Icon log */}
        {data.isRunning && (
          <Box className={stylesCss.logIcon} onClick={handleLogClick} title='Xem log'>
            <IconTerminal2 size={18} />
          </Box>
        )}

        {/* Icon Container */}
        <Box className={iconContainerClass}>
          <img src={iconSrc} alt={data.label} className={stylesCss.nodeIcon} />
          {data.isRunning && <Box className={stylesCss.runningPulse} />}
        </Box>

        {/* Content Container */}
        <Box className={stylesCss.contentContainer}>
          <KanbanText lineClamp={2} size='sm' fw={600} className={stylesCss.nodeLabel}>
            {data.label}
          </KanbanText>

          {data.subtitle && (
            <KanbanText size='xs' lineClamp={1} className={stylesCss.nodeSubtitle}>
              {data.subtitle}
            </KanbanText>
          )}

          {/* Running Status Text */}
          {data.isRunning && (
            <KanbanText size='xs' className={stylesCss.runningText}>
              Running...
            </KanbanText>
          )}
        </Box>

        {/* Error Warning Triangle with Tooltip */}
        {isError && !data.isRunning && (
          <Tooltip label={errorDetails} position='top' withArrow multiline className={stylesCss.errorTooltip}>
            <Box className={stylesCss.errorIcon}>
              <IconAlertTriangle size={18} color='red' />
            </Box>
          </Tooltip>
        )}

        {/* Status Indicator */}
        <Box className={statusIndicatorClass} />

        {/* Target Handle - For incoming connections (Left) */}
        <Handle type='target' position={Position.Left} className={targetHandleClass} id='target-left' />

        {/* Source Handle - For outgoing connections (Right) */}
        <Handle type='source' position={Position.Right} className={sourceHandleClass} id='source-right' />

        {/* Connection Hints */}
        <Box className={stylesCss.connectionHints}>
          <Box className={leftHintClass}>
            <Box className={stylesCss.hintDot} />
          </Box>
          <Box className={rightHintClass}>
            <Box className={stylesCss.hintDot} />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default CustomNode;
