import { EntityAction } from '@common/constants/EntityActionConstants';
import { buildChangeDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { ColumnType, KanbanButton, KanbanIconButton, KanbanTable, KanbanTableProps, KanbanText, renderDateTime } from 'kanban-design-system';
import React, { useCallback, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import stylesCss from './WorkflowPage.module.scss';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { ChangeWorkflow, ChangeWorkflowSchema, ChangeWorkflowTypeEnum } from '@core/schema/ChangeWorkflowNode';
import useFetch from '@core/hooks/useFetch';
import { Box, Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import { IconEye } from '@tabler/icons-react';
import useMutate from '@core/hooks/useMutate';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import AmtModal from '@components/AmtModal';
import { WorkflowForm } from './WorkflowForm';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { IconEditCircle } from '@tabler/icons-react';
import { WorkflowData } from './WorkflowDetailPage';

type WorkFlowPageProps = {
  id?: number;
};

const columns: ColumnType<ChangeWorkflow>[] = [
  {
    name: 'name',
    title: 'Name',
  },
  {
    name: 'type',
    title: 'Type',
  },
  {
    name: 'assignUserString',
    title: 'Assign',
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    customRender: renderDateTime,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
    hidden: true,
  },
];

export const WorkflowPage: React.FC<WorkFlowPageProps> = () => {
  const changeId = Number(useParams().id);

  const DEFAULT_FORM_VALUE: ChangeWorkflow = {
    name: '',
    changeId: changeId,
    workflowData: undefined,
    type: ChangeWorkflowTypeEnum.CHECKLIST,
  };

  const [updateMode, setUpdateMode] = useState(false);
  const [openedWorkflowModal, { close: closeWorkflowModal, open: openWorkflowModal }] = useDisclosure(false);

  const form = useForm<ChangeWorkflow>({
    resolver: zodResolver(ChangeWorkflowSchema),
    mode: 'onChange',
    defaultValues: DEFAULT_FORM_VALUE,
  });
  const { reset } = form;

  const { mutate: saveWorkflowMutate } = useMutate(ChangeRequestApi.saveWorkflow, {
    successNotification: 'Workflow saved successfully',
    onSuccess: () => {
      refetchList();
      closeWorkflowModal();
    },
  });

  const navigate = useNavigate();
  const { data: workflows, refetch: refetchList } = useFetch(ChangeRequestApi.findAllWorkflows(changeId), {
    enabled: !!changeId,
  });

  const { mutate: deleteWorkflowMutate } = useMutate(ChangeRequestApi.deleteWorkflow, {
    successNotification: 'Workflow deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => {
      refetchList();
    },
  });

  const tableProps: KanbanTableProps<ChangeWorkflow> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeWorkflow> = {
      columns: columns,
      data: workflows?.data || [],
      actions: {
        customAction: (data) => {
          return (
            <Flex gap='xs' align='center'>
              <Tooltip label='View'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL, changeId, data.id || 0, EntityAction.VIEW));
                  }}>
                  <IconEye />
                </KanbanIconButton>
              </Tooltip>
              <Tooltip label='Edit'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setUpdateMode(true);
                    reset(data);
                    openWorkflowModal();
                  }}>
                  <IconEditCircle />
                </KanbanIconButton>
              </Tooltip>
              <Tooltip label='Edit workflow'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL, changeId, data.id || 0, EntityAction.EDIT));
                  }}>
                  <IconEdit />
                </KanbanIconButton>
              </Tooltip>
              <Tooltip label='Delete'>
                <KanbanIconButton
                  variant='transparent'
                  color='red'
                  size={'sm'}
                  onClick={() => {
                    deleteWorkflowMutate({ changeId: changeId, workflowId: data.id || 0 }, { confirm: deleteConfirm([data.name || '']) });
                  }}>
                  <IconTrash />
                </KanbanIconButton>
              </Tooltip>
            </Flex>
          );
        },
      },
    };
    return tblProps;
  }, [changeId, workflows?.data, deleteWorkflowMutate, navigate, openWorkflowModal, reset]);

  const handleSave = useCallback(async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }
    const workflowToSave = form.getValues() as WorkflowData;
    saveWorkflowMutate(workflowToSave);
  }, [form, saveWorkflowMutate]);

  return (
    <div className={stylesCss.workflowPage}>
      <KanbanButton
        size='xs'
        variant='light'
        onClick={() => {
          setUpdateMode(false);
          reset(DEFAULT_FORM_VALUE);
          openWorkflowModal();
        }}
        className={stylesCss.createButton}
        leftSection={<IconPlus size={10} />}>
        Create workflow
      </KanbanButton>
      <AmtModal
        size='40%'
        opened={openedWorkflowModal}
        onClose={closeWorkflowModal}
        title={`${updateMode ? 'Edit' : 'Create'} workflow`}
        centered
        actions={
          <KanbanButton
            variant='filled'
            onClick={() => {
              handleSave();
            }}>
            Save
          </KanbanButton>
        }>
        <Box onClick={(e) => e.stopPropagation()} onDoubleClick={(e) => e.stopPropagation()}>
          <WorkflowForm form={form} />
        </Box>
      </AmtModal>
      {workflows?.data?.length !== 0 && <KanbanTable {...tableProps} />}
      {workflows?.data?.length === 0 && <KanbanText>No workflow yet. Click the button to create a new workflow</KanbanText>}
    </div>
  );
};
