/**
 * [K-tool gencode][EN] API for Jenkins job tree.
 * <AUTHOR> gencode
 * @created_date 2025-07-25
 */

import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createListSchema, createResponseSchema, ResponseData } from '@core/schema/Common';
import { JenkinsJobTree, JenkinsJobTreeSchema } from '@core/schema/JenkinsTree';
import { RequestConfig } from '@core/api';

export class JenkinsApi {
  static getTree(changeNodeId: number, parent?: string): RequestConfig<ResponseData<JenkinsJobTree[]>> {
    return createRequest({
      url: `${BaseURL.integrationJenkins}/tree`,
      method: 'GET',
      params: { changeNodeId: changeNodeId, parent: parent },
      schema: createResponseSchema(createListSchema(JenkinsJobTreeSchema)),
    });
  }
}
