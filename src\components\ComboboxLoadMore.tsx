import React, { ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import { Box, CloseButton, Combobox, Flex, Pill, PillsInput, ScrollArea, useCombobox } from '@mantine/core';
import { IconCheck } from '@tabler/icons-react';
import { useDebounceCallback } from 'kanban-design-system';
import { groupBy, uniqBy } from 'lodash';

export const DEFAULT_DROPDOWN_SCROLL_THRESHOLD = 20;
export const DEFAULT_VISIBLE_OPTIONS_COUNT = 10;
export const DEFAULT_DEBOUNCE_TIME = 600;
export const DEFAULT_MAX_PILL_TO_SHOW = 2;
export const DEFAULT_MAX_HEIGHT_PILL_TO_SHOW = 80;

const DEFAULT_RENDER_LOADING = () => <div>Loading...</div>;
const DEFAULT_RENDER_EMPTY = () => <Combobox.Empty>Nothing found</Combobox.Empty>;

export type ComboboxLoadMoreProps<T extends ComboboxLoadMoreData> = {
  onChange: (selectedValues: T[]) => void;
  label?: React.ReactNode;
  placeholder?: string;
  options: T[];
  renderPillLabel: (data: T) => ReactNode;
  renderPillProps?: (data: T) => { bg?: string; bd?: string; c?: string; [key: string]: any };
  renderOptionLabel?: (data: T) => string;
  renderOptionIcon?: (data: T) => React.ReactNode;
  renderEmpty?: React.ReactNode;
  renderLoading?: React.ReactNode;
  dropdownScrollThreshold?: number;
  debounceTime?: number;
  pillToShow?: number;
  values: T[];
  onSearch: (value: string) => void;
  onScroll: () => void;
  onBlur?: () => void;
  onDropdownOpen?: () => void;
  onClearValue?: (ids: string[]) => void;
  maxSelection?: number;
  groupByKeys?: (keyof T)[];
  clearable?: boolean;
  isLoading?: boolean;
  checkIconPosition?: 'left' | 'right';
  comboboxProps?: React.ComponentPropsWithoutRef<'div'>;
  description?: React.ReactNode;
  descriptionProps?: Record<string, any>;
  disabled?: boolean;
  dropdownOpened?: boolean;
  filter?: (value: string, item: T) => boolean;
  inputSize?: string;
  labelProps?: Record<string, any>;
  maxDropdownHeight?: string | number;
  nothingFoundMessage?: React.ReactNode;
  pointer?: boolean;
  radius?: number | string;
  required?: boolean;
  rightSection?: React.ReactNode;
  rightSectionProps?: React.ComponentPropsWithoutRef<'div'>;
  rightSectionWidth?: React.CSSProperties['width'];
  searchable?: boolean;
  scrollableForValue?: boolean;
  maxHeightValue?: number;
  onClickedOption?: () => void;
  clearSearchWhenBlur?: boolean;
  error?: string;
};

export type ComboboxLoadMoreData = {
  id: string;
  name: string;
};
type ComboboxOptionProps<T extends ComboboxLoadMoreData> = {
  item: T;
  isSelected: boolean;
  renderLabel?: (data: T) => string;
  renderIcon?: (data: T) => React.ReactNode;
  checkIconPosition?: 'left' | 'right';
  disabled?: boolean;
};
const ComboboxOption = <T extends ComboboxLoadMoreData>({
  checkIconPosition = 'left',
  disabled,
  isSelected,
  item,
  renderIcon,
  renderLabel,
}: ComboboxOptionProps<T>) => (
  <Combobox.Option disabled={disabled} value={item.id.toString()} key={item.id.toString()}>
    <Flex align='center'>
      {isSelected && checkIconPosition === 'left' && <IconCheck size={16} style={{ marginRight: 8 }} />}
      {renderIcon && <Box mr={8}>{renderIcon(item)}</Box>}
      {renderLabel ? renderLabel(item) : item.name}
      {isSelected && checkIconPosition === 'right' && <IconCheck size={16} style={{ marginLeft: 8 }} />}
    </Flex>
  </Combobox.Option>
);

const ComboboxLoadMore = <T extends ComboboxLoadMoreData>({
  checkIconPosition = 'left',
  clearable = true,
  clearSearchWhenBlur = false,
  comboboxProps,
  debounceTime = DEFAULT_DEBOUNCE_TIME,
  description,
  descriptionProps,
  disabled = false,
  dropdownScrollThreshold = DEFAULT_DROPDOWN_SCROLL_THRESHOLD,
  error,
  groupByKeys = [],
  inputSize,
  isLoading = false,
  label,
  labelProps,
  maxHeightValue = DEFAULT_MAX_HEIGHT_PILL_TO_SHOW,
  maxSelection = Infinity,
  nothingFoundMessage,
  onBlur,
  onChange,
  onClearValue,
  onClickedOption,
  onDropdownOpen,
  onScroll,
  onSearch,
  options,
  pillToShow = DEFAULT_MAX_PILL_TO_SHOW,
  placeholder,
  pointer = false,
  radius,
  renderEmpty = DEFAULT_RENDER_EMPTY(),
  renderLoading = DEFAULT_RENDER_LOADING(),
  renderOptionIcon,
  renderOptionLabel,
  renderPillLabel,
  renderPillProps,
  required = false,
  rightSection,
  rightSectionProps,
  rightSectionWidth,
  scrollableForValue = false,
  searchable = true,
  values,
}: ComboboxLoadMoreProps<T>) => {
  const combobox = useCombobox({
    onDropdownOpen: () => {
      if (onDropdownOpen) {
        onDropdownOpen();
      }
    },
  });
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearch = useDebounceCallback(onSearch, debounceTime);
  const debouncedScroll = useDebounceCallback(onScroll, debounceTime);
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const [preserveScrollPosition, setPreserveScrollPosition] = useState<number | null>(null);
  const previousOptionsLength = useRef(options.length);

  // Effect to preserve scroll position when new options are loaded
  useEffect(() => {
    if (options.length > previousOptionsLength.current && preserveScrollPosition !== null && dropdownRef.current) {
      // New options were added (infinite scroll), restore scroll position
      // Use requestAnimationFrame to ensure DOM has updated
      requestAnimationFrame(() => {
        if (dropdownRef.current) {
          dropdownRef.current.scrollTop = preserveScrollPosition;
          setPreserveScrollPosition(null);
        }
      });
    }
    previousOptionsLength.current = options.length;
  }, [options.length, preserveScrollPosition]);

  // Handling scroll in the dropdown
  const handleScrollPositionChange = (position: { x: number; y: number }) => {
    if (dropdownRef?.current) {
      const { clientHeight, scrollHeight } = dropdownRef.current;
      const maxScrollPosition = scrollHeight - clientHeight;
      if (position.y >= maxScrollPosition - dropdownScrollThreshold) {
        // Save current scroll position before triggering load more
        setPreserveScrollPosition(position.y);
        debouncedScroll();
      }
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.currentTarget.value;
    setSearchTerm(value);
    // Reset scroll position tracking when search changes
    setPreserveScrollPosition(null);
    debouncedSearch(value);
  };

  const handleClearAll = () => {
    setSearchTerm('');
    onChange([]);
    if (onClearValue) {
      onClearValue(values.map((v) => v.id));
    }
  };

  const handleOptionSelect = useCallback(
    (val: string) => {
      const selectedOption = options.find((item) => item.id === val) as T;
      if (selectedOption) {
        const isAlreadySelected = values.some((item) => item.id === selectedOption.id);
        if (maxSelection === 1) {
          onChange(isAlreadySelected ? [] : [selectedOption]);
        } else {
          if (!isAlreadySelected && values.length >= maxSelection) {
            return;
          }
          onChange(isAlreadySelected ? values.filter((item) => item.id !== selectedOption.id) : [...values, selectedOption]);
        }
        setSearchTerm('');
        if (onClickedOption) {
          onClickedOption();
        }
        combobox.resetSelectedOption();
      }
    },
    [options, values, maxSelection, onClickedOption, combobox, onChange],
  );

  const groupByKeysFn = (item: T) => {
    const groupLabel = groupByKeys
      .map((key) => item[key])
      .join(' | ')
      .trim();
    return groupLabel;
  };

  const groupedOptions = groupBy(uniqBy(options, 'id'), groupByKeysFn);

  // handle remove value
  const handleRemove = (valueId: string) => {
    if (onClearValue && !combobox.dropdownOpened) {
      onClearValue([valueId]);
    }
    onChange(values.filter((v) => v.id !== valueId));
    combobox.resetSelectedOption();
  };

  const pillsToDisplay = scrollableForValue ? values : values.slice(0, pillToShow);
  const remainingPillsCount = values.length - pillToShow;
  return (
    <Box {...comboboxProps}>
      <Combobox disabled={disabled} store={combobox} onOptionSubmit={handleOptionSelect}>
        <Combobox.Target>
          <PillsInput
            label={label}
            onClick={() => combobox.openDropdown()}
            onKeyDown={(event) => {
              if (event.key === 'Backspace' && !searchTerm) {
                onChange(values.slice(0, -1));
                combobox.resetSelectedOption();
              }
            }}
            rightSection={values.length > 0 && clearable ? <CloseButton onClick={handleClearAll} /> : rightSection || <Combobox.Chevron />}
            {...descriptionProps}
            description={description}
            size={inputSize}
            disabled={disabled}
            required={required}
            pointer={pointer}
            radius={radius}
            {...labelProps}
            rightSectionWidth={rightSectionWidth}
            rightSectionProps={rightSectionProps}
            error={error}>
            {/* Conditional ScrollArea around Pill.Group */}
            <Pill.Group>
              {scrollableForValue ? (
                <ScrollArea.Autosize mah={maxHeightValue} type='scroll'>
                  {pillsToDisplay.map((value) => {
                    const customProps = renderPillProps ? renderPillProps(value) : {};
                    // Only set default bg if no custom bg is provided
                    const defaultProps = disabled && !customProps.bg ? { bg: 'white' } : {};
                    return (
                      <Pill key={value.id} withRemoveButton={!disabled} onRemove={() => handleRemove(value.id)} {...defaultProps} {...customProps}>
                        {renderPillLabel(value)}
                      </Pill>
                    );
                  })}
                </ScrollArea.Autosize>
              ) : (
                <>
                  {pillsToDisplay.map((value) => {
                    const customProps = renderPillProps ? renderPillProps(value) : {};
                    // Only set default bg if no custom bg is provided
                    const defaultProps = disabled && !customProps.bg ? { bg: 'white' } : {};
                    return (
                      <Pill key={value.id} withRemoveButton={!disabled} onRemove={() => handleRemove(value.id)} {...defaultProps} {...customProps}>
                        {renderPillLabel(value)}
                      </Pill>
                    );
                  })}
                  {remainingPillsCount > 0 && <Pill>+{remainingPillsCount} more</Pill>}
                </>
              )}
            </Pill.Group>
            {searchable && (
              <Combobox.EventsTarget>
                <PillsInput.Field
                  disabled={disabled}
                  onFocus={() => combobox.openDropdown()}
                  onBlur={() => {
                    onBlur && onBlur();
                    if (clearSearchWhenBlur && searchTerm) {
                      setSearchTerm('');
                      onSearch('');
                    }
                    combobox.closeDropdown();
                  }}
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder={placeholder}
                />
              </Combobox.EventsTarget>
            )}
          </PillsInput>
        </Combobox.Target>
        <Combobox.Dropdown>
          {isLoading ? (
            renderLoading
          ) : options.length === 0 ? (
            renderEmpty || nothingFoundMessage
          ) : (
            <ScrollArea.Autosize viewportRef={dropdownRef} mah={200} type='scroll' onScrollPositionChange={handleScrollPositionChange}>
              {Object.keys(groupedOptions).map((groupLabel) => (
                <Combobox.Group key={groupLabel} label={groupLabel}>
                  {groupedOptions[groupLabel].map((option) => (
                    <ComboboxOption
                      disabled={disabled}
                      key={option.id}
                      item={option}
                      isSelected={values.some((value) => value.id === option.id)}
                      renderLabel={renderOptionLabel}
                      renderIcon={renderOptionIcon}
                      checkIconPosition={checkIconPosition}
                    />
                  ))}
                </Combobox.Group>
              ))}
            </ScrollArea.Autosize>
          )}
        </Combobox.Dropdown>
      </Combobox>
    </Box>
  );
};

export default ComboboxLoadMore;
