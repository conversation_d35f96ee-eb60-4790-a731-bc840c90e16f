import React, { useState, useCallback, useEffect, memo } from 'react';
import { Modal, Tree, Group, RenderTreeNodePayload, Button, useTree } from '@mantine/core';
import { KanbanButton } from 'kanban-design-system';
import { IconFolder, IconFile, IconChevronRight, IconGitBranch, IconChevronDown } from '@tabler/icons-react';
import { JenkinsA<PERSON> } from '@api/JenkinsApi';
import useFetch from '@core/hooks/useFetch';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import styles from './JenkinsBrowseJobsModal.module.scss';
import { JenkinsJobTree } from '@core/schema/JenkinsTree';

interface JenkinsBrowseJobsModalProps {
  changeNodeId?: number;
  application?: string;
  onJobSelect: (jobUrl: string, parameters: JenkinsParameter[]) => void;
  disabled?: boolean;
}

interface TreeNode {
  value: string;
  label: string;
  hasChildren: boolean;
  children?: TreeNode[];
  clazz?: string;
  parameters?: JenkinsParameter[];
  level?: number;
  isLoaded?: boolean;
}

interface JenkinsParameter {
  name: string;
  type: string;
  defaultValue: string | number | boolean | null;
  description?: string;
  choices?: string[];
}

const FOLDER_TYPES = ['com.cloudbees.hudson.plugins.folder.Folder', 'org.jenkinsci.plugins.workflow.multibranch.WorkflowMultiBranchProject'];
const JOB_TYPES = ['org.jenkinsci.plugins.workflow.job.WorkflowJob'];
const MULTIBRANCH_TYPE = 'org.jenkinsci.plugins.workflow.multibranch.WorkflowMultiBranchProject';

const isFolder = (clazz: string) => FOLDER_TYPES.includes(clazz);
const isJob = (clazz: string) => JOB_TYPES.includes(clazz);
const isMultiBranch = (clazz: string) => clazz === MULTIBRANCH_TYPE;

const extractParams = (rawParams: unknown): JenkinsParameter[] => {
  if (!Array.isArray(rawParams)) {
    return [];
  }

  return rawParams.map((param) => ({
    name: String(param.name || ''),
    type: String(param.type || ''),
    defaultValue: ['string', 'number', 'boolean'].includes(typeof param.defaultValue)
      ? param.defaultValue
      : param.defaultValue
        ? String(param.defaultValue)
        : null,
    description: param.description ? String(param.description) : undefined,
    choices: Array.isArray(param.choices) ? param.choices.map(String) : undefined,
  }));
};

const mapToTreeNode = (items: JenkinsJobTree[], level = 0): TreeNode[] => {
  return items.map((item) => ({
    value: item.url,
    label: item.name,
    hasChildren: isFolder(item.clazz),
    clazz: item.clazz,
    parameters: extractParams(item.parameters),
    level,
    isLoaded: false,
  }));
};

const updateChildren = (nodes: TreeNode[], nodeValue: string, children: TreeNode[]): TreeNode[] => {
  return nodes.map((node) => {
    if (node.value === nodeValue) {
      return { ...node, children, isLoaded: true };
    }
    if (node.children?.length) {
      return { ...node, children: updateChildren(node.children, nodeValue, children) };
    }
    return node;
  });
};

const findNode = (nodes: TreeNode[], value: string): TreeNode | null => {
  for (const node of nodes) {
    if (node.value === value) {
      return node;
    }
    if (node.children) {
      const found = findNode(node.children, value);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

const JenkinsBrowseJobsModal: React.FC<JenkinsBrowseJobsModalProps> = memo(({ application, changeNodeId, disabled = false, onJobSelect }) => {
  const [opened, setOpened] = useState(false);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [loadingNodes, setLoadingNodes] = useState<Set<string>>(new Set());
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [currentPath, setCurrentPath] = useState<string | undefined>();
  const [shouldFetch, setShouldFetch] = useState(false);

  const tree = useTree();

  const { data, error, isFetched } = useFetch(JenkinsApi.getTree(changeNodeId || 0, currentPath), {
    enabled: shouldFetch && !!changeNodeId && application === ChangeApplicationTypeEnum.JENKINS,
    showLoading: false,
  });

  const resetState = useCallback(() => {
    setTreeData([]);
    setLoadingNodes(new Set());
    setExpandedNodes(new Set());
    setCurrentPath(undefined);
    setShouldFetch(false);
  }, []);

  const loadData = useCallback((path?: string) => {
    setCurrentPath(path);
    setLoadingNodes((prev) => new Set([...prev, path || 'root']));
    setShouldFetch(true);
  }, []);

  const handleToggle = useCallback(
    (node: TreeNode) => {
      if (!node.hasChildren) {
        return;
      }

      const isExpanded = expandedNodes.has(node.value);

      if (isExpanded) {
        setExpandedNodes((prev) => {
          const newSet = new Set(prev);
          newSet.delete(node.value);
          return newSet;
        });
        tree.collapse(node.value);
      } else {
        setExpandedNodes((prev) => new Set([...prev, node.value]));
        tree.expand(node.value);

        if (!node.isLoaded) {
          loadData(node.value);
        }
      }
    },
    [tree, loadData, expandedNodes],
  );

  const handleJobSelect = useCallback(
    (node: TreeNode) => {
      if (!isJob(node.clazz || '')) {
        return;
      }

      const jobUrl = node.value.replace(/\/$/, '');
      onJobSelect(jobUrl, node.parameters || []);
      setOpened(false);
      resetState();
    },
    [onJobSelect, resetState],
  );

  const renderNode = useCallback(
    ({ elementProps, node }: RenderTreeNodePayload) => {
      const treeNode = node as TreeNode;
      const { clazz = '', label, level = 0, value } = treeNode;

      const nodeIsJob = isJob(clazz);
      const nodeIsFolder = isFolder(clazz);
      const nodeIsMultiBranch = isMultiBranch(clazz);
      const isExpanded = expandedNodes.has(value);
      const isLoading = loadingNodes.has(value);

      let icon;
      if (nodeIsJob) {
        icon = <IconFile size={16} />;
      } else if (nodeIsMultiBranch) {
        icon = <IconGitBranch size={18} />;
      } else if (nodeIsFolder) {
        icon = <IconFolder size={18} />;
      } else {
        icon = <IconFile size={16} />;
      }

      return (
        <Group {...elementProps} gap={8} className={styles.treeNodeRow} style={{ paddingLeft: 12 + level * 24 }}>
          {(nodeIsFolder || nodeIsMultiBranch) && (
            <div
              onClick={(e) => {
                e.stopPropagation();
                handleToggle(treeNode);
              }}
              className={styles.expandToggle}>
              {isLoading ? <div className={styles.spinnerXs} /> : isExpanded ? <IconChevronDown size={12} /> : <IconChevronRight size={12} />}
            </div>
          )}

          {icon}

          <span
            onClick={(e) => {
              e.stopPropagation();
              if (nodeIsJob) {
                handleJobSelect(treeNode);
              } else if (nodeIsFolder || nodeIsMultiBranch) {
                handleToggle(treeNode);
              }
            }}
            className={styles.nodeLabel}
            style={{ fontWeight: nodeIsJob ? 600 : 400 }}>
            {label}
          </span>

          {nodeIsJob && <div className={styles.badgeJob}>Job</div>}
          {nodeIsMultiBranch && <div className={styles.badgeMultiBranch}>MultiBranch</div>}
        </Group>
      );
    },
    [expandedNodes, loadingNodes, handleToggle, handleJobSelect],
  );

  useEffect(() => {
    if (!isFetched) {
      return;
    }

    setLoadingNodes((prev) => {
      const newSet = new Set(prev);
      newSet.delete(currentPath || 'root');
      return newSet;
    });

    setShouldFetch(false);

    if (data?.data && Array.isArray(data.data)) {
      if (currentPath === undefined) {
        setTreeData(mapToTreeNode(data.data, 0));
      } else {
        const parentNode = findNode(treeData, currentPath);
        if (parentNode) {
          const childNodes = mapToTreeNode(data.data, (parentNode.level || 0) + 1);
          setTreeData((prev) => updateChildren(prev, currentPath, childNodes));

          tree.expand(currentPath);
        }
      }
    }
  }, [data, error, currentPath, treeData, tree, isFetched]);

  const isLoading = loadingNodes.has('root');
  const hasError = error && isFetched;
  const hasData = treeData.length > 0;

  return (
    <>
      <KanbanButton
        leftSection={<IconFolder size={16} />}
        onClick={() => {
          setOpened(true);
          if (!hasData) {
            loadData();
          }
        }}
        variant='outline'
        className={styles.browseButton}
        loading={isLoading}
        disabled={disabled}>
        Browse Jobs
      </KanbanButton>

      <Modal
        opened={opened}
        onClose={() => {
          setOpened(false);
          resetState();
        }}
        title='Select Jenkins Job'
        size='lg'
        centered
        overlayProps={{ opacity: 0.55, blur: 3 }}>
        <p className={styles.modalSubtitle} style={{ marginBottom: 16 }}>
          Navigate through folders and multibranch projects to find your job
        </p>

        <div className={styles.treeContainer}>
          {isLoading ? (
            <div className={styles.loadingState}>
              <div className={styles.loadingCenter}>
                <div className={styles.spinnerSm} />
                Loading Jenkins jobs...
              </div>
            </div>
          ) : hasError ? (
            <div className={styles.errorState}>
              <div>
                <div className={styles.errorIcon}>❌</div>
                <div>Failed to load Jenkins jobs</div>
                <Button size='sm' variant='outline' onClick={() => loadData()} className={styles.retryBtn}>
                  Retry
                </Button>
              </div>
            </div>
          ) : !hasData ? (
            <div className={styles.emptyState}>No jobs found</div>
          ) : (
            <Tree data={treeData} tree={tree} levelOffset={0} expandOnClick={false} renderNode={renderNode} />
          )}
        </div>

        <div className={styles.modalFooter}>
          {hasData && !isLoading && !hasError ? 'Click on folders/multibranch to expand • Click on jobs to select' : 'Jenkins job browser'}
        </div>
      </Modal>
    </>
  );
});

JenkinsBrowseJobsModal.displayName = 'JenkinsBrowseJobsModal';

export default JenkinsBrowseJobsModal;
