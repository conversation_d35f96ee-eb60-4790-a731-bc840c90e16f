/* Reviewed by: K-tool gencode */
import { z } from 'zod';
import { ChangeRequestWorkflowDetailExecuteSchema } from './ChangeRequestWorkflowDetailExecute';
import { WorkflowNodeStatusEnum } from './WorkflowNodeStatusEnum';
/* Created by: K-tool gencode */
export enum WorkflowExecutionTypeEnum {
  ALL = 'ALL',
  NODE_ONLY = 'NODE_ONLY',
  MANUAL = 'MANUAL',
}

export const ChangeRequestWorkflowExecuteSchema = z.object({
  id: z.number().optional(),
  workflowId: z.number(),
  workflowName: z.string().optional().nullable(),
  workflowData: z.string(),
  startTime: z.string().optional().nullable(),
  endTime: z.string().optional().nullable(),
  status: z.nativeEnum(WorkflowNodeStatusEnum).default(WorkflowNodeStatusEnum.NOT_STARTED),
  details: z.array(ChangeRequestWorkflowDetailExecuteSchema).default([]),
});

export type ChangeRequestWorkflowExecute = z.infer<typeof ChangeRequestWorkflowExecuteSchema>;
