/* Định dạng cho tên trường */
.fieldName {
  width: 100px;
  overflow: hidden;
  // text-overflow: ellipsis;
  // white-space: nowrap;
  align-items: center;
  justify-content: flex-start;
  font-weight: 500;
  cursor: grab;
}


.richTextEditor {
  min-height: 120px;
  flex-grow: 1;
}

/* Định dạng cho multiline */
.multiLineTextArea {
  resize: vertical;
  max-height: 120px;
  flex-grow: 1;
}

/* Định dạng cho trường nhập liệu */
.inputField {
  flex-grow: 1;
  gap: xs;
  min-width: 0;
  //120px is containt width of field name
  max-width: calc(100% - 120px);
}

/* Định dạng cho tay cầm kéo */
.dragHandle {
  cursor: grab;
  align-items: center;
  touch-action: none;
  opacity: 0.8;
}

/* Định dạng cho phần tử giấy trong lưới */
.gridItemPaper {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
}

.fieldGroup {
  width: 100%;
  height: 100%;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  gap: 0;
  padding-right: var(--mantine-spacing-xs);
  padding-top: var(--mantine-spacing-xs);
  padding-bottom: var(--mantine-spacing-xs);
}

.gridItemPaper {
  position: relative;
  border-radius: 2px;
}

.errorRichText {
  position: absolute;
  top: 50%;
  right: var(--mantine-spacing-xs);
  transform: translateY(-50%);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
}