import { z } from 'zod';
import { ChangeRequestReviewStatusEnum } from '@common/constants/ChangeRequestReviewStatusConstants';
import { ChangeRequestReviewApprovalStatusEnum } from '@common/constants/ChangeRequestReviewApprovalStatusConstants';
import { COMMENT_MAX_LENGTH, COMMON_MAX_LENGTH, NOTE_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';

export const ChangeRequestReviewApproverSchema = z.object({
  approver: z.string().optional(),
  approvalStatus: ChangeRequestReviewApprovalStatusEnum.nullish(),
  fullName: z.string().optional(),
});

export const ChangeRequestReviewSchema = z.object({
  owner: z.string().nullish(),
  ownerFullName: z.string().nullish(),
  status: ChangeRequestReviewStatusEnum,
  note: z.string().nullish(),
  documentName: z.string().nullish(),
  approvers: z.array(ChangeRequestReviewApproverSchema).nullish(),
  comment: z.string().nullish(),
  skipFileUpload: z.boolean().optional().default(false).nullish(),
});

export const CoordinatorReviewRequestSchema = z.object({
  owner: z.string().min(1).max(COMMON_MAX_LENGTH),
  note: z.string().min(1).max(NOTE_MAX_LENGTH),
});

// Owner extends Coordinator (reuse)
export const OwnerReviewRequestSchema = CoordinatorReviewRequestSchema.extend({
  approvers: z.array(z.string().min(1).max(COMMON_MAX_LENGTH)).nonempty(),
  skipFileUpload: z.boolean().optional().default(false),
});

// Approver action schema
export const ApproverStatusRequestSchema = z.object({
  approvalStatus: ChangeRequestReviewApprovalStatusEnum,
  comment: z.string().min(1).max(COMMENT_MAX_LENGTH),
});

// Review status update schema
export const ChangeRequestReviewStatusRequestSchema = z.object({
  status: ChangeRequestReviewStatusEnum.refine((val) => val !== null, {
    message: INPUT_REQUIRE,
  }),
});

// Types
export type ChangeRequestReviewApprover = z.infer<typeof ChangeRequestReviewApproverSchema>;
export type ChangeRequestReview = z.infer<typeof ChangeRequestReviewSchema>;
export type CoordinatorReviewRequest = z.infer<typeof CoordinatorReviewRequestSchema>;
export type OwnerReviewRequest = z.infer<typeof OwnerReviewRequestSchema>;
export type ApproverStatusRequest = z.infer<typeof ApproverStatusRequestSchema>;
export type ChangeRequestReviewStatusRequest = z.infer<typeof ChangeRequestReviewStatusRequestSchema>;
