import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { useDisclosure } from '@mantine/hooks';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { buildDetailUrl, buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import useFetch from '@core/hooks/useFetch';
import { ChangeRequestDocumentGroupApi } from '@api/ChangeRequestDocumentGroupApi';
import { ChangeRequestDocumentEnums, ChangeRequestDocumentRole } from '@models/ChangeRequestDocumentGroupModel';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import DocumentGroupViewPage from '@pages/change/request/document/DocumentGroupViewPage';
import DocumentGroupEditPage from '@pages/change/request/document/DocumentGroupEditPage';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { ACL_PERMISSIONS } from '@common/constants/AclPermissionConstants';
import { KanbanText } from 'kanban-design-system';
import { Center } from '@mantine/core';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import { DocumentProvider } from '@context/DocumentPermissionContext';
import { ChangeStageTypeEnum } from '@common/constants/ChageStageType';

// Props for View/Edit components
export interface CommonTabProps {
  changeRequestId: number;
  canSaveChange: boolean;
  canEditChangeDocument: boolean;
  handleExistWithoutSave: () => void;
  handleEdit: () => void;
  handleSave: () => void;
  handleNavigateToList: () => void;
  onRefetchDocumentGroups: () => void;
}

interface ChangeRequestDocumentTabProps {
  focusAccordionId?: string | null;
  setIsShowSidebar: React.Dispatch<React.SetStateAction<boolean>>;
}

const ChangeRequestDocumentTab: React.FC<ChangeRequestDocumentTabProps> = ({ focusAccordionId, setIsShowSidebar }) => {
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [searchParams] = useSearchParams();
  const [openedItems, setOpenedItems] = useState<string[]>([]);
  const currentAction = (searchParams.get('action') as EntityAction) || EntityAction.VIEW;

  const { id } = useParams();
  const changeRequestId = Number(id);
  const navigate = useNavigate();

  const { data, refetch } = useFetch(ChangeRequestDocumentGroupApi.findAll(changeRequestId), {
    enabled: !!changeRequestId,
  });

  const roles = useMemo(() => data?.data?.roles ?? [], [data]);
  const documentGroups = useMemo(() => data?.data?.groups ?? [], [data]);
  const stage = useMemo(() => data?.data?.stage, [data]);

  useEffect(() => {
    if (documentGroups) {
      setOpenedItems(documentGroups.map((g) => g.id.toString()));
    }
  }, [documentGroups]);

  const canSaveChange = useCheckPermissons(changeRequestId ? [ACL_PERMISSIONS.CHANGE_UPDATE] : [ACL_PERMISSIONS.CHANGE_ADD]);
  const canEditChange = useCheckPermissons([ACL_PERMISSIONS.CHANGE_UPDATE]);

  const hasMatchingRole = useCallback((role: ChangeRequestDocumentRole) => roles.includes(role), [roles]);

  const canEditChangeDocument = useMemo(() => {
    return (
      ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING === stage &&
      (hasMatchingRole(ChangeRequestDocumentEnums.Role.enum.COORDINATOR) || hasMatchingRole(ChangeRequestDocumentEnums.Role.enum.OWNER)) &&
      canEditChange
    );
  }, [canEditChange, hasMatchingRole, stage]);

  const PageComponent = useMemo(() => {
    if (currentAction === EntityAction.VIEW) {
      return DocumentGroupViewPage;
    }

    if ([EntityAction.EDIT, EntityAction.CREATE].includes(currentAction)) {
      if (canEditChangeDocument) {
        return DocumentGroupEditPage;
      }
      return null;
    }

    return null;
  }, [canEditChangeDocument, currentAction]);

  useEffect(() => {
    if (currentAction === EntityAction.VIEW) {
      setIsShowSidebar(true);
    } else if ([EntityAction.EDIT, EntityAction.CREATE].includes(currentAction)) {
      if (canEditChangeDocument && hasMatchingRole(ChangeRequestDocumentEnums.Role.enum.COORDINATOR)) {
        setIsShowSidebar(false);
      } else {
        setIsShowSidebar(true);
      }
    }
  }, [currentAction, canEditChangeDocument, hasMatchingRole, setIsShowSidebar]);

  const handleEdit = useCallback(() => {
    navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.EDIT));
  }, [navigate, changeRequestId]);

  const handleSave = useCallback(() => {
    navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.VIEW));
  }, [navigate, changeRequestId]);

  const handleNavigateToList = useCallback(() => {
    navigate(buildUrl(ROUTE_PATH.CHANGE_REQUEST), { state: buildNavigateState({ fromDetail: true }) });
  }, [navigate]);

  return PageComponent ? (
    <DocumentProvider
      openedItems={openedItems}
      setOpenedItems={setOpenedItems}
      roles={roles}
      documentGroups={documentGroups}
      focusAccordionId={focusAccordionId}>
      <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleNavigateToList} />
      <PageComponent
        canSaveChange={canSaveChange}
        canEditChangeDocument={canEditChangeDocument}
        changeRequestId={changeRequestId}
        onRefetchDocumentGroups={refetch}
        handleEdit={handleEdit}
        handleSave={handleSave}
        handleExistWithoutSave={openModal}
        handleNavigateToList={handleNavigateToList}
      />
    </DocumentProvider>
  ) : (
    <Center mt='xl'>
      <KanbanText c='dimmed' fw={500} fz='lg'>
        No permission
      </KanbanText>
    </Center>
  );
};

export default ChangeRequestDocumentTab;
