import AmtModal from '@components/AmtModal';
import { <PERSON><PERSON>, Box, Flex, Stack, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import React, { useEffect, useMemo, useState } from 'react';
import ExtendCiInformation from './ExtendCiInformation';
import useFetch from '@core/hooks/useFetch';
import { ItCmdbApi } from '@api/ItCmdbApi';
import { IconAlertHexagon } from '@tabler/icons-react';
import { CiInformationType, MAX_LENGTH_CI_IMPACTED, ModeCI, ModeCIType, SERVICE_AFFECTED } from '@core/schema/ItCmdb';
import { CustomNodeData } from './reactFlow/CustomNode';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { KanbanContentLoading } from 'kanban-design-system';

const getNameString = (arr: { ciImpactedName: string }[]): string => {
  if (!arr || arr.length === 0) {
    return 'No data found';
  }
  const names = arr.map((item) => item.ciImpactedName);
  if (names.length <= 5) {
    return `${names.join(', ')} `;
  } else {
    return `${names.slice(0, MAX_LENGTH_CI_IMPACTED).join(', ')},... `;
  }
};

type CiInformationProps = {
  customNodeData: CustomNodeData | undefined;
  setCustomNodeData: (data: React.SetStateAction<CustomNodeData | undefined>) => void;
};

export const CiInformation: React.FC<CiInformationProps> = ({ customNodeData, setCustomNodeData }) => {
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [mode, setMode] = useState<ModeCIType>(ModeCI.CI_IMPACTED);

  const value = useMemo(() => {
    if (customNodeData?.application === ChangeApplicationTypeEnum.JENKINS) {
      return customNodeData.parameters?.jobPath || null;
    } else if (customNodeData?.application === ChangeApplicationTypeEnum.WLA) {
      return customNodeData.parameters?.taskName || null;
    } else {
      return null;
    }
  }, [customNodeData?.application, customNodeData?.parameters]);
  const nodeId = customNodeData?.changeNodeId || -1;

  const { data: cisData, isFetching } = useFetch(ItCmdbApi.getCisInformation(nodeId, value || ''), {
    enabled: nodeId > 0 && !!value,
    showLoading: false,
  });

  // TODO: Save data for displaying in APPROVAL stage
  useEffect(() => {
    setCustomNodeData((prev) => {
      if (!prev) {
        return prev;
      }
      return {
        ...prev,
        settings: {
          ...prev.settings,
          cis: cisData?.data as CiInformationType,
        },
      };
    });
  }, [cisData?.data, setCustomNodeData]);

  const cisImpacted = useMemo(() => cisData?.data?.cisImpacted || [], [cisData?.data]);
  const cisInChange = useMemo(() => cisData?.data?.cisInChange || [], [cisData?.data]);
  const servicesAffected = useMemo(() => cisImpacted?.filter((item) => item.ciTypeImpactedName === SERVICE_AFFECTED) || [], [cisImpacted]);
  const isError = cisInChange.length === 0;
  const icon = <IconAlertHexagon />;

  return (
    <KanbanContentLoading loading={isFetching}>
      <Stack>
        {isError && (
          <Alert color='red' title='Error' icon={icon}>
            Can not find any CI in change
          </Alert>
        )}

        {!isError && (
          <Box>
            <Flex>
              <Text fw={'bold'} size='sm' style={{ flex: 1 }}>
                CIs in change:{' '}
              </Text>
              <Text size='sm' style={{ flex: 2 }}>
                {cisInChange?.map((item) => item.name).join(', ')}
              </Text>
            </Flex>
            <Flex mt={'var(--mantine-spacing-sm)'}>
              <Text fw={'bold'} size='sm' style={{ flex: 1 }}>
                CIs Impacted:{' '}
              </Text>
              <Text style={{ flex: 2 }} size='sm'>
                {getNameString(cisImpacted)}
                <Text
                  hidden={cisImpacted.length === 0}
                  span
                  c={'var(--mantine-color-violet-7)'}
                  style={{ cursor: 'pointer', textDecoration: 'underline' }}
                  onClick={() => {
                    setMode(ModeCI.CI_IMPACTED);
                    openModal();
                  }}>
                  Show detail
                </Text>
              </Text>
            </Flex>
            <Flex mt={'var(--mantine-spacing-sm)'}>
              <Text fw={'bold'} size='sm' style={{ flex: 1 }}>
                Service Affected:{' '}
              </Text>
              <Text style={{ flex: 2 }} size='sm'>
                {getNameString(servicesAffected)}
                <Text
                  hidden={servicesAffected.length === 0}
                  span
                  c={'var(--mantine-color-violet-7)'}
                  style={{ cursor: 'pointer', textDecoration: 'underline' }}
                  onClick={() => {
                    setMode(ModeCI.SERVICE_AFFECTED);
                    openModal();
                  }}>
                  Show detail
                </Text>
              </Text>
            </Flex>
          </Box>
        )}

        <AmtModal size='80%' opened={openedModal} onClose={closeModal} title={mode} centered>
          <ExtendCiInformation data={mode === ModeCI.SERVICE_AFFECTED ? servicesAffected : cisImpacted} />
        </AmtModal>
      </Stack>
    </KanbanContentLoading>
  );
};

export default CiInformation;
