import { z } from 'zod';
export enum WebsocketDataTypeEnum {
  JEN<PERSON>IN_LOG = 'JENKIN_LOG',
  JENKIN_STAGE = 'JENKIN_STAGE',
  WLA_LOG = 'WLA_LOG',
  WLA_INSTANCE = 'WLA_INSTANCE',
}

export enum JenkinsStageStatusEnum {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  ABORTED = 'ABORTED',
  SKIPPED = 'SKIPPED',
  UNSTABLE = 'UNSTABLE',
  PAUSED = 'PAUSED',
  CANCELLED = 'CANCELLED',
}

export enum WlaStatusEnum {
  DEFINED = 'DEFINED',
  WAITING = 'WAITING',
  TIME_WAIT = 'TIME_WAIT',
  INSTANCE_WAIT = 'INSTANCE_WAIT',
  HELD = 'HELD',
  EXCLUSIVE_REQUESTED = 'EXCLUSIVE_REQUESTED',
  EXCLUSIVE_WAIT = 'EXCLUSIVE_WAIT',
  RESOURCE_REQUESTED = 'RESOURCE_REQUESTED',
  RESOURCE_WAIT = 'RESOURCE_WAIT',
  EXECUTION_WAIT = 'EXECUTION_WAIT',
  UNDELIVERABLE = 'UNDELIVERABLE',
  QUEUED = 'QUEUED',
  SUBMITTED = 'SUBMITTED',
  STEP_RESTARTED = 'STEP_RESTARTED',
  ACTION_REQUIRED = 'ACTION_REQUIRED',
  STARTED = 'STARTED',
  RUNNING = 'RUNNING',
  RUNNING_PROBLEMS = 'RUNNING_PROBLEMS',
  CANCEL_PENDING = 'CANCEL_PENDING',
  IN_DOUBT = 'IN_DOUBT',
  START_FAILURE = 'START_FAILURE',
  CONFIRMATION_REQUIRED = 'CONFIRMATION_REQUIRED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
  SKIPPED = 'SKIPPED',
  FINISHED = 'FINISHED',
  SUCCESS = 'SUCCESS',
}

export const JenkinsStageDataSchema = z.object({
  name: z.string(),
  status: z.nativeEnum(JenkinsStageStatusEnum),
  startTimeMillis: z.number().nullish(),
  durationMillis: z.number().nullish(),
  errorMessage: z.string().nullish(),
});

export const JenkinsStageSchema = z.object({
  nodeId: z.string().optional(),
  datas: z.array(JenkinsStageDataSchema),
});

//WLA
export const WlaTaskInstanceSchema = z.object({
  name: z.string(),
  sysId: z.string(),
  status: z.nativeEnum(WlaStatusEnum),
  type: z.string(),
});

export const WlaInstanceSchema = z.object({
  nodeId: z.string().optional(),
  instances: z.array(WlaTaskInstanceSchema),
});

export const WlaLogSchema = z.object({
  fullLog: z.string(),
  sysId: z.string(),
});

export const WebsocketDataSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal(WebsocketDataTypeEnum.JENKIN_LOG),
    content: z.string(),
  }),
  z.object({
    type: z.literal(WebsocketDataTypeEnum.JENKIN_STAGE),
    content: JenkinsStageSchema,
  }),
  z.object({
    type: z.literal(WebsocketDataTypeEnum.WLA_LOG),
    content: WlaLogSchema,
  }),
  z.object({
    type: z.literal(WebsocketDataTypeEnum.WLA_INSTANCE),
    content: WlaInstanceSchema,
  }),
]);

export type WebsocketData = z.infer<typeof WebsocketDataSchema>;

export type JenkinsStageData = z.infer<typeof JenkinsStageDataSchema>;
export type JenkinsStage = z.infer<typeof JenkinsStageSchema>;
export type WlaInstance = z.infer<typeof WlaInstanceSchema>;
export type WlaTaskInstance = z.infer<typeof WlaTaskInstanceSchema>;
export type WlaLog = z.infer<typeof WlaLogSchema>;
