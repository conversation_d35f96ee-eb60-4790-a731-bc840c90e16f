import React, { useCallback, useEffect, useMemo } from 'react';
import { Box, Stack } from '@mantine/core';
import { Controller, UseFormReturn } from 'react-hook-form';
import { CustomContentComponent } from '@components/customContent/CustomContentComponent';
import { ControllerRenderProps } from 'react-hook-form';
import { TextLengthConstants } from '@common/constants/TextLengthConstants';
import { KanbanMultiSelect } from 'kanban-design-system';
import { MappedUser } from '@pages/change/request/role/ChangeRequestDetailRole';
import { EmailTemplateTypeEnum } from '@common/constants/EmailTemplateConstants';
import { NotificationsEmailTemplateApi } from '@api/NotificationsEmailTemplateApi';
import useFetch from '@core/hooks/useFetch';
import { ApprovalNotificationFormModel } from '@models/ApprovalNotificationFormModel';

export interface ApprovalNotificationFormProps {
  form: UseFormReturn<ApprovalNotificationFormModel>;
  changeRequestId: number;
  changeFlowNodeId: number;
  selectedUsers: MappedUser[];
}

export const ApprovalNotificationForm = ({ changeFlowNodeId, changeRequestId, form, selectedUsers }: ApprovalNotificationFormProps) => {
  const { data: emailTemplateResponse } = useFetch(NotificationsEmailTemplateApi.findByType(EmailTemplateTypeEnum.enum.APPROVAL_EMAIL), {
    errorNotification: {
      enable: false,
    },
  });

  const usernames = useMemo(
    () => selectedUsers.map((user) => user.username).filter((name): name is string => !!name && name.trim() !== ''),
    [selectedUsers],
  );

  const { clearErrors, control, formState, reset } = form;

  useEffect(() => {
    if (emailTemplateResponse?.data) {
      reset({
        subject: emailTemplateResponse.data.subject || '',
        content: emailTemplateResponse.data.content || '',
        changeFlowNodeId: changeFlowNodeId,
        changeRequestId: changeRequestId,
        usernames: usernames,
      });
    }

    // Cleanup function - reset form when component unmounts
    return () => {
      reset({
        subject: '',
        content: '',
        changeFlowNodeId: changeFlowNodeId,
        changeRequestId: changeRequestId,
        usernames: usernames,
      });
    };
  }, [changeFlowNodeId, changeRequestId, emailTemplateResponse, reset, usernames]);

  const handleChangeField = useCallback(
    (field: keyof ApprovalNotificationFormModel, value: string, fieldControl: ControllerRenderProps<ApprovalNotificationFormModel>) => {
      if (formState.errors[field]) {
        clearErrors(field);
      }
      fieldControl.onChange(value);
    },
    [formState.errors, clearErrors],
  );

  return (
    <Stack>
      <form>
        <Box>
          <KanbanMultiSelect
            value={selectedUsers.map((user) => user.displayUsername).filter((name): name is string => !!name && name.trim() !== '')}
            label='Notify to'
            withAsterisk
            disabled
          />
        </Box>
        <Controller
          control={control}
          name={'subject'}
          render={({ field }) => (
            <CustomContentComponent
              key={`subject-${changeRequestId}-${changeFlowNodeId}`} // Unique key for re-rendering
              label='Subject'
              value={field.value || ''}
              disabled={false}
              docsToolbar={false}
              singleLineMode={true}
              onChange={(val) => handleChangeField('subject', val, field)}
              error={formState.errors['subject']?.message}
              contentMaxLength={TextLengthConstants.MAX_LENGTH_1000}
              withAsterisk
            />
          )}
        />
        <Controller
          control={control}
          name={'content'}
          render={({ field }) => (
            <CustomContentComponent
              key={`content-${changeRequestId}-${changeFlowNodeId}`} // Unique key for re-rendering
              label='Content'
              value={field.value || ''}
              onChange={(val) => handleChangeField('content', val, field)} // Ensure onChange is called
              contentMaxLength={TextLengthConstants.EMAIL_CONTENT_MAX_LENGTH}
              docsToolbar={true}
              disabled={false}
              error={formState.errors['content']?.message}
              minHeightRte={300}
              withAsterisk
            />
          )}
        />
      </form>
    </Stack>
  );
};
