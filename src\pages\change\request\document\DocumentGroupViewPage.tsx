import React, { useMemo, useState } from 'react';
import { Box, Flex } from '@mantine/core';
import { KanbanButton, KanbanCheckbox, KanbanIconButton, KanbanTitle } from 'kanban-design-system';
import { IconArrowLeft } from '@tabler/icons-react';
import { CommonTabProps } from '@pages/change/request/document/index';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import DocumentAccordionView from './components/DocumentAccordionView';
import useMutate from '@core/hooks/useMutate';
import { ChangeRequestDocumentGroupApi } from '@api/ChangeRequestDocumentGroupApi';
import {
  ChangeRequestActionTarget,
  ChangeRequestDocumentActionStatusType,
  ChangeRequestDocumentApproverRejectRequest,
  ChangeRequestDocumentApproverRejectRequestSchema,
  ChangeRequestDocumentEnums,
  ChangeRequestSendToApproverRequest,
  ChangeRequestSendToOwnerRequest,
  LimitedActionStatus,
} from '@models/ChangeRequestDocumentGroupModel';
import CommentFormModal from '@pages/change/request/document/components/CommentFormModal';
import AmtModal from '@components/AmtModal';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toggleArrayItem } from '@pages/change/request/document/utils/TableUtils';
import { useChangeRequestDocumentPermission } from '@core/hooks/useChangeRequestDocumentPermission';
import { useDocumentContext } from '@context/DocumentPermissionContext';

export interface ChangeRequestOwnerSelection {
  referenceId: number;
  username: string;
  checked: boolean;
}

export interface ChangeRequestDocumentSelection {
  groupId: number;
  docId: number;
  checked: boolean;
  documentApproverLevel: number | null | undefined;
  documentStatus: ChangeRequestDocumentActionStatusType;
}

const smallBtn = { size: 'xs' as const, variant: 'filled' as const };

const DocumentGroupViewPage = ({
  canEditChangeDocument,
  changeRequestId,
  handleEdit,
  handleNavigateToList,
  onRefetchDocumentGroups,
}: CommonTabProps) => {
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const { documentGroups } = useDocumentContext();
  const { currentUser, isApprover, isCoordinator, isGroupApprover, isOwner } = useChangeRequestDocumentPermission();
  const [selectedOwners, setSelectedOwners] = useState<ChangeRequestSendToOwnerRequest[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<ChangeRequestSendToApproverRequest[]>([]);
  const [selectedDocumentApprovers, setSelectedDocumentApprovers] = useState<ChangeRequestActionTarget[]>([]);
  const form = useForm<ChangeRequestDocumentApproverRejectRequest>({
    resolver: zodResolver(ChangeRequestDocumentApproverRejectRequestSchema),
    defaultValues: {
      action: null,
      role: null,
      targets: [],
      comment: '',
    },
    mode: 'onBlur',
  });

  const { control, getValues, handleSubmit, reset } = form;

  const { mutate: sendToOwner } = useMutate(
    ({ changeRequestId, data }: { changeRequestId: number; data: ChangeRequestSendToOwnerRequest[] }) =>
      ChangeRequestDocumentGroupApi.sendToOwner(changeRequestId, data),
    {
      successNotification: 'Send to owner successfully',
      onSuccess: () => {
        setSelectedOwners([]);
        setSelectedDocumentApprovers([]);
        onRefetchDocumentGroups();
      },
    },
  );

  const { mutate: sendToApprover } = useMutate(
    ({ changeRequestId, data }: { changeRequestId: number; data: ChangeRequestSendToApproverRequest[] }) =>
      ChangeRequestDocumentGroupApi.sendToApprover(changeRequestId, data),
    {
      successNotification: 'Send to approver successfully',
      onSuccess: () => {
        setSelectedDocuments([]);
        onRefetchDocumentGroups();
      },
    },
  );

  const { mutate: submitMutate } = useMutate(
    ({ changeRequestId }: { changeRequestId: number }) => ChangeRequestDocumentGroupApi.submitDocumentApprover(changeRequestId),
    {
      successNotification: 'Submit successfully',
      onSuccess: () => {
        onRefetchDocumentGroups();
      },
    },
  );

  const { mutate: approveOrRejectMutate } = useMutate(
    ({ changeRequestId, data }: { changeRequestId: number; data: ChangeRequestDocumentApproverRejectRequest }) =>
      ChangeRequestDocumentGroupApi.approveOrReject(changeRequestId, data),
    {
      successNotification: `Action successfully`,
      errorNotification: (data) => ({ message: data.message }),
      onSuccess: () => {
        setSelectedDocuments([]);
        setSelectedDocumentApprovers([]);
        onRefetchDocumentGroups();
        closeModal();
      },
    },
  );

  const handleActionApproveOrReject = (
    action: ChangeRequestDocumentActionStatusType,
    isActionAll: boolean,
    docId?: number,
    documentApproverLevel?: number,
  ) => {
    reset({
      action,
      role: ChangeRequestDocumentEnums.Role.Enum.DOCUMENT_APPROVER,
      targets: !isActionAll ? [{ referenceId: docId, documentApproverLevel }] : selectedDocumentApprovers,
      comment: '',
    });
    openModal();
  };

  const handleToggleOwner = ({ checked, referenceId, username }: ChangeRequestOwnerSelection) => {
    setSelectedOwners((prev) =>
      toggleArrayItem(
        prev,
        (i) => i.referenceId === referenceId,
        (item) => {
          const usernames = item?.usernames ?? [];
          if (checked) {
            return { referenceId, usernames: usernames.includes(username) ? usernames : [...usernames, username] };
          }
          const newNames = usernames.filter((u) => u !== username);
          return newNames.length ? { referenceId, usernames: newNames } : null;
        },
      ),
    );
  };

  const handleToggleDocument = ({ checked, docId, documentApproverLevel, documentStatus, groupId }: ChangeRequestDocumentSelection) => {
    if (LimitedActionStatus.includes(documentStatus) && documentApproverLevel !== null) {
      setSelectedDocumentApprovers((prev) =>
        toggleArrayItem(
          prev,
          (i) => i.referenceId === docId,
          () => (checked ? { referenceId: docId, documentApproverLevel } : null),
        ),
      );
    }
    setSelectedDocuments((prev) =>
      toggleArrayItem(
        prev,
        (i) => i.groupId === groupId,
        (item) => {
          const docIds = item?.docIds ?? [];
          if (checked) {
            return { groupId, docIds: docIds.includes(docId) ? docIds : [...docIds, docId] };
          }
          const newIds = docIds.filter((id) => id !== docId);
          return newIds.length ? { groupId, docIds: newIds } : null;
        },
      ),
    );
  };

  const allApproverTargets = useMemo(() => {
    const seen = new Set<string>();
    const result: ChangeRequestActionTarget[] = [];

    documentGroups.forEach((group) => {
      group.owners.forEach((owner) => {
        (owner.documents ?? []).forEach((doc) => {
          (doc.approvers ?? []).forEach((appr) => {
            if (appr.user.username === currentUser && LimitedActionStatus.includes(appr.status) && appr.documentApproverLevel !== null) {
              const key = `${doc.id}-${appr.documentApproverLevel}`;
              if (!seen.has(key)) {
                seen.add(key);
                result.push({
                  referenceId: doc.id,
                  documentApproverLevel: appr.documentApproverLevel,
                });
              }
            }
          });
        });
      });
    });

    return result;
  }, [documentGroups, currentUser]);

  const { allChecked, indeterminate } = useMemo(() => {
    const total = allApproverTargets.length;
    const selected = selectedDocumentApprovers.length;
    return {
      allChecked: total > 0 && selected === total,
      indeterminate: selected > 0 && selected < total,
    };
  }, [allApproverTargets, selectedDocumentApprovers]);

  const buildDocumentsByGroup = (groups: typeof documentGroups): ChangeRequestSendToApproverRequest[] => {
    const docsByGroup: Record<number, number[]> = {};
    groups.forEach((group) => {
      group.owners.forEach((owner) => {
        owner.documents?.forEach((doc) => {
          if (!docsByGroup[group.id]) {
            docsByGroup[group.id] = [];
          }
          docsByGroup[group.id].push(doc.id);
        });
      });
    });
    return Object.entries(docsByGroup).map(([groupId, docIds]) => ({
      groupId: Number(groupId),
      docIds,
    }));
  };

  const handleToggleAllDocuments = (checked: boolean) => {
    if (checked) {
      setSelectedDocumentApprovers(allApproverTargets);
      if (allApproverTargets.length > 0) {
        setSelectedDocuments(buildDocumentsByGroup(documentGroups));
      }
    } else {
      setSelectedDocuments([]);
      setSelectedDocumentApprovers([]);
    }
  };

  const onSubmit = handleSubmit((values) => {
    approveOrRejectMutate({ changeRequestId, data: values });
  });

  return (
    <Flex direction='column' justify='flex-end'>
      <HeaderTitleComponent
        title=''
        leftSection={
          <Flex align='center' gap='xs'>
            <KanbanIconButton size='sm' variant='subtle' onClick={handleNavigateToList}>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz='h4'>Change documents</KanbanTitle>
          </Flex>
        }
        rightSection={
          <Flex align='center' gap='xs'>
            {canEditChangeDocument && (
              <KanbanButton {...smallBtn} onClick={handleEdit}>
                Edit
              </KanbanButton>
            )}
            {isCoordinator && (
              <KanbanButton
                {...smallBtn}
                disabled={selectedOwners.length === 0}
                onClick={() =>
                  sendToOwner({
                    changeRequestId,
                    data: selectedOwners,
                  })
                }>
                Send to owner
              </KanbanButton>
            )}
            {isOwner && (
              <KanbanButton
                {...smallBtn}
                disabled={selectedDocuments.length === 0 && isOwner}
                onClick={() =>
                  sendToApprover({
                    changeRequestId,
                    data: selectedDocuments,
                  })
                }>
                Send to leader
              </KanbanButton>
            )}
            {(isApprover || isGroupApprover) && (
              <KanbanButton
                {...smallBtn}
                onClick={() => {
                  submitMutate({
                    changeRequestId,
                  });
                }}>
                Submit
              </KanbanButton>
            )}
          </Flex>
        }
      />
      <Box p='md'>
        {documentGroups && documentGroups.length > 0 && (
          <>
            <KanbanTitle mb='lg'>Document</KanbanTitle>
            <Flex justify='flex-start' align='center' mb='md' gap='md'>
              <KanbanCheckbox
                checked={allChecked}
                indeterminate={indeterminate}
                label={'Select all document categories'}
                onChange={(e) => handleToggleAllDocuments(e.target.checked)}
              />
              <KanbanButton
                {...smallBtn}
                disabled={selectedDocumentApprovers.length === 0}
                onClick={() => {
                  handleActionApproveOrReject(ChangeRequestDocumentEnums.ActionStatus.Enum.APPROVED, true);
                }}>
                Approve
              </KanbanButton>
              <KanbanButton
                {...smallBtn}
                disabled={selectedDocumentApprovers.length === 0}
                onClick={() => {
                  handleActionApproveOrReject(ChangeRequestDocumentEnums.ActionStatus.Enum.REJECTED, true);
                }}>
                Reject
              </KanbanButton>
            </Flex>
          </>
        )}
        <DocumentAccordionView
          changeRequestId={changeRequestId}
          onToggleOwner={handleToggleOwner}
          onToggleDocument={handleToggleDocument}
          selectedOwners={selectedOwners}
          selectedDocuments={selectedDocuments}
          handleActionApproveOrReject={handleActionApproveOrReject}
        />
      </Box>
      <AmtModal
        size='80%'
        opened={openedModal}
        onClose={closeModal}
        centered
        title={'Action'}
        actions={
          <KanbanButton variant='filled' onClick={onSubmit}>
            Confirm
          </KanbanButton>
        }>
        <Box onClick={(e) => e.stopPropagation()} onDoubleClick={(e) => e.stopPropagation()}>
          <CommentFormModal action={getValues('action')} control={control} />
        </Box>
      </AmtModal>
    </Flex>
  );
};

export default DocumentGroupViewPage;
