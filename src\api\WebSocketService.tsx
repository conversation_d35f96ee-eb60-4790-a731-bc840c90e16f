import { RTS_URL } from '@common/constants/BaseUrl';
import KeycloakService from '@core/auth/Keycloak';
import { WebsocketData, WebsocketDataSchema } from '@core/schema/WebsocketData';
import { Client } from '@stomp/stompjs';
import type { IMessage } from '@stomp/stompjs';
import SockJS from 'sockjs-client';

const QUEUE_WEBSOCKET_MESSAGE_DESTINATION_TOPIC = '/user/queue/message';
const PUBLIC_TOPIC = '/topic/public';
const SEND_TOPIC = '/app/send';
const keycloakService = KeycloakService;
class WebSocketService {
  private client: Client | null = null;
  private isConnected = false;
  private websocketCallback: ((notification: WebsocketData) => void)[] = [];

  constructor() {
    console.info('Initializing WebSocket service...');
    this.initializeClient();
  }

  private initializeClient() {
    const wsUrl = `${RTS_URL}/ws`;

    const token = keycloakService.getToken();
    if (!token) {
      console.error('No authentication token found');
      return;
    }

    this.client = new Client({
      webSocketFactory: () => new SockJS(wsUrl),
      connectHeaders: {
        Authorization: `Bearer ${token}`,
      },
      reconnectDelay: 5000, // automatic reconnect every 5s
      heartbeatIncoming: 60000,
      heartbeatOutgoing: 60000,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.client) {
      return;
    }

    this.client.onConnect = () => {
      console.info('WebSocket Connected Successfully');
      this.isConnected = true;
      this.subscribeToNotifications();
    };

    this.client.onStompError = (frame: { headers: Record<string, string>; body: string }) => {
      if (frame.body.includes('401')) {
        console.error('STOMP Error: Authentication failed', frame);
      } else {
        console.error('STOMP Error: Failed to connect to notification service', frame);
      }
    };

    this.client.onWebSocketError = (event: Event) => {
      console.error('WebSocket Error:', event);
    };

    this.client.onWebSocketClose = () => {
      console.info('WebSocket Connection Closed');
      this.isConnected = false;
    };
  }

  private subscribeToNotifications() {
    if (!this.client?.connected) {
      console.error('Cannot subscribe: WebSocket not connected');
      return;
    }

    this.client.subscribe(QUEUE_WEBSOCKET_MESSAGE_DESTINATION_TOPIC, this.handleNotification);
    this.client.subscribe(PUBLIC_TOPIC, this.handleNotification);
  }

  private handleNotification = (message: IMessage) => {
    const notification = WebsocketDataSchema.safeParse(JSON.parse(message.body));
    if (!notification.success) {
      console.error('Failed to parse notification:', notification.error);
      return;
    }

    this.websocketCallback.forEach((cb) => cb(notification.data));
  };

  public connect() {
    if (!this.client) {
      console.error('WebSocket client not initialized');
      return;
    }

    try {
      this.client.activate();
      console.info('WebSocket activation initiated');
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
    }
  }

  public disconnect() {
    if (!this.client) {
      console.error('WebSocket client not initialized');
      return;
    }

    try {
      this.client.deactivate();
      this.isConnected = false;
      console.info('WebSocket disconnected successfully');
    } catch (error) {
      console.error('Failed to disconnect from WebSocket:', error);
    }
  }

  public isWebSocketConnected(): boolean {
    return this.isConnected;
  }

  public sendNotification(notification: Omit<WebsocketData, 'id' | 'read' | 'createdAt'>) {
    if (!this.client?.connected) {
      console.error('Cannot send notification: WebSocket not connected');
      return;
    }

    this.client.publish({
      destination: SEND_TOPIC,
      body: JSON.stringify(notification),
    });
  }

  public registerNotificationHandler(callback: (notification: WebsocketData) => void) {
    this.websocketCallback.push(callback);
  }

  public unregisterNotificationHandler(callback: (notification: WebsocketData) => void) {
    this.websocketCallback = this.websocketCallback.filter((cb) => cb !== callback);
  }
}

let webSocketServiceInstance: WebSocketService | null = null;

export const createWebSocketService = (): WebSocketService => {
  if (!webSocketServiceInstance) {
    webSocketServiceInstance = new WebSocketService();
  }
  return webSocketServiceInstance;
};

export const getWebSocketService = (): WebSocketService | null => {
  return webSocketServiceInstance;
};

export const resetWebSocketService = (): void => {
  webSocketServiceInstance = null;
};
