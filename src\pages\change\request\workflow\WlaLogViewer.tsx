// WlaLogViewer.tsx
import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Paper, Flex, Text, Box, ScrollArea, Stack } from '@mantine/core';
import { IconX, IconTerminal2, IconChevronDown } from '@tabler/icons-react';
import { KanbanIconButton, KanbanText } from 'kanban-design-system';
import stylesCss from './WlaLogViewer.module.scss';
import type { Node } from '@xyflow/react';
import useRegisterWebsocketListener from '@core/hooks/useRegisterWebsocketListener';
import { WebsocketDataTypeEnum, WlaLog, WlaTaskInstance } from '@core/schema/WebsocketData';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

type WlaLogViewerProps = {
  node: Node;
  onClose: () => void;
};

export const WlaLogViewer: React.FC<WlaLogViewerProps> = ({ node, onClose }) => {
  const [opened, setOpened] = useState<number | null>(null);
  const [instances, setInstances] = useState<WlaTaskInstance[]>([]);
  const [selectedSysId, setSelectedSysId] = useState<string | null>(null);

  // Map sysId -> WlaLog
  const [logMap, setLogMap] = useState<Record<string, WlaLog>>({});

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const consoleViewportRef = useRef<HTMLDivElement | null>(null);

  // Lắng nghe websocket
  useRegisterWebsocketListener((message) => {
    if (message.type === WebsocketDataTypeEnum.WLA_LOG && message.content) {
      const content = message.content as WlaLog;
      if (content.sysId) {
        setLogMap((prev) => ({ ...prev, [content.sysId]: content }));
      }
    }
    if (message.type === WebsocketDataTypeEnum.WLA_INSTANCE) {
      setInstances(message.content.instances ?? []);
    }
  });

  // Chọn mặc định instance đầu tiên nếu chưa chọn
  useEffect(() => {
    if (!selectedSysId && instances.length > 0) {
      setSelectedSysId(instances[0].sysId);
      setOpened(0);
    }
  }, [instances, selectedSysId]);

  // Log hiện tại theo instance đang chọn
  const currentLog = useMemo(() => {
    if (!selectedSysId) {
      return undefined;
    }
    return logMap[selectedSysId];
  }, [selectedSysId, logMap]);

  // Auto-scroll log của instance đang chọn
  useEffect(() => {
    const el = consoleViewportRef.current;
    if (!el) {
      return;
    }
    const distanceFromBottom = el.scrollHeight - el.scrollTop - el.clientHeight;
    const isNearBottom = distanceFromBottom < 80;
    if (isNearBottom) {
      el.scrollTo({ top: el.scrollHeight, behavior: 'smooth' });
    }
  }, [currentLog?.fullLog]);

  // --- FIX: Wrap PanelGroup in a full-height div, do not set height: 100% on Panel itself ---
  return (
    <div className={stylesCss.panelGroupFullHeight}>
      <PanelGroup direction='vertical'>
        {/* Main content panel (optional) */}
        <Panel minSize={0} collapsible={true} />
        <PanelResizeHandle className={stylesCss.resizeHandle} />
        <Panel defaultSize={50} minSize={10} maxSize={90}>
          <Paper ref={containerRef} className={stylesCss.paperPanel} radius='md' withBorder>
            {/* Header */}
            <Flex justify='space-between' align='center' className={stylesCss.header}>
              <KanbanText size='xl' fw={700} className={stylesCss.title}>
                Log: {`${node?.data?.label ?? ''}`}
              </KanbanText>
              <KanbanIconButton onClick={onClose} variant='subtle' size='sm' aria-label='Close log viewer'>
                <IconX size={18} />
              </KanbanIconButton>
            </Flex>

            {/* Body: Sidebar (instances) | Console */}
            <Flex className={stylesCss.body}>
              {/* Left block: Instances */}
              <Box className={stylesCss.stagesSection}>
                <ScrollArea className={stylesCss.instancesScrollArea} scrollbarSize={8} scrollHideDelay={500} type='always'>
                  <Stack gap='sm' p='sm'>
                    {instances?.map((instance, idx) => {
                      const isActive = opened === idx;
                      const isSelected = selectedSysId === instance.sysId;
                      return (
                        <Paper
                          key={instance.sysId || instance.name + idx}
                          p='xs'
                          radius='md'
                          withBorder
                          data-status={instance.status}
                          className={[
                            stylesCss.stagePaper,
                            isActive ? stylesCss.stagePaperActive : '',
                            stylesCss.stagePaperCustom,
                            isActive ? stylesCss.stagePaperActiveCustom : '',
                            isSelected ? stylesCss.stagePaperSelected : '',
                          ].join(' ')}
                          onClick={() => {
                            setOpened(isActive ? null : idx);
                            setSelectedSysId(instance.sysId);
                            // (Optional) nếu có API yêu cầu log theo sysId, có thể gửi request ở đây
                            // requestWlaLog(instance.sysId)
                          }}>
                          <Flex justify='space-between' align='center'>
                            <Text size='sm' fw={500} truncate='end' style={{ flex: 1 }}>
                              {instance.name}
                            </Text>
                            <IconChevronDown size={14} className={`${stylesCss.chevron} ${isActive ? stylesCss.chevronOpen : ''}`} />
                          </Flex>
                          <Text size='xs' className={stylesCss.stageStatus} data-status={instance.status} fw={500} mt={4}>
                            {typeof instance.status === 'string' ? instance.status : ''}
                          </Text>
                        </Paper>
                      );
                    })}
                  </Stack>
                </ScrollArea>
              </Box>

              {/* Right block: Console Log */}
              <Box className={stylesCss.console}>
                {/* Console Header */}
                <Flex align='center' gap='xs' p='xs' className={stylesCss.consoleHeader}>
                  <IconTerminal2 size={16} />
                  <span className={stylesCss.consoleHeaderTitle}>Console Log {selectedSysId ? `· ${selectedSysId}` : ''}</span>
                </Flex>

                {/* Scrollable Log Content */}
                <ScrollArea ref={consoleViewportRef} className={stylesCss.scrollArea} scrollbarSize={8} scrollHideDelay={500} type='always'>
                  <Box p='xs'>
                    <pre className={stylesCss.logPre}>{currentLog?.fullLog ?? 'No logs available...'}</pre>
                  </Box>
                </ScrollArea>
              </Box>
            </Flex>
          </Paper>
        </Panel>
      </PanelGroup>
    </div>
  );
};

export default WlaLogViewer;
