import React, { useEffect } from 'react';
import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/spotlight/styles.css';
import '@mantine/dropzone/styles.css';
import { MantineProvider } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { Default } from 'core/themes/Default';
import PageLoadingComponent from '@components/PageLoadingComponent';
import { ZIndexNotification } from '@common/constants/ZIndexConstants';
import { getConfigs } from '@core/configs/Configs';
import { KanbanDesignSystemProvider, KanbanModalProvider } from 'kanban-design-system';
import { QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import Index from './pages';
import { queryClient } from '@core/configs/QueryClient';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import '@mantine/tiptap/styles.css';
import '@xyflow/react/dist/style.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { FORMAT_DD_MM_YYYY, FORMAT_DD_MM_YYYY_HH_MM } from '@common/constants/DateTimeConstants';

const appName = getConfigs().fullname;
function App() {
  useEffect(() => {
    const styles = ['font-size: 18px', 'color: red'];
    // eslint-disable-next-line no-console
    console.log(
      `%cWelcome to the \`${appName}\` project, developed by \`Kanban\` team. Please notify the administrator if there is a problem`,
      styles.join(';'),
    );
  }, []);

  return (
    <>
      <MantineProvider theme={Default}>
        <KanbanModalProvider>
          <KanbanDesignSystemProvider
            settings={{
              date: {
                defaultDateFormat: FORMAT_DD_MM_YYYY,
                defaultDateTimeFormat: FORMAT_DD_MM_YYYY_HH_MM,
              },
            }}>
            <QueryClientProvider client={queryClient}>
              <Notifications limit={5} zIndex={ZIndexNotification} />
              <PageLoadingComponent />
              <BrowserRouter>
                <Index />
              </BrowserRouter>
              <ReactQueryDevtools initialIsOpen={false} />
            </QueryClientProvider>
          </KanbanDesignSystemProvider>
        </KanbanModalProvider>
      </MantineProvider>
    </>
  );
}

export default App;
