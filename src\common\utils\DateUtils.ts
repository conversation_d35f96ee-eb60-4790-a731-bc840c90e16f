import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat'; // ES 2015
dayjs.extend(customParseFormat);

export const DATE_FORMAT_SIMPLE = 'DD/MM/YYYY';

export const FILTER_YEAR_START: number = 1900;
export const FILTER_YEAR_END: number = 2100;
export const FILTER_MONTH_START: number = 1;
export const FILTER_MONTH_END: number = 12;
export const FILTER_DAY_START: number = 1;
export const FILTER_DAY_END: number = 31;

export const dateToString = (date?: Date) => {
  return date ? dayjs(date).format(DATE_FORMAT_SIMPLE) : '';
};

export const stringToDate = (str?: string) => {
  return str && isParseable(str) ? stringToDayJs(str)?.toDate() : undefined;
};
export const stringToDayJs = (str?: string) => {
  return str && isParseable(str) ? dayjs(str, DATE_FORMAT_SIMPLE) : undefined;
};

export const isParseable = (str?: string) => {
  return str ? dayjs(str, DATE_FORMAT_SIMPLE).isValid() : false;
};

export const isoStringToDate = (str?: string) => {
  return str ? dayjs(str).toDate() : undefined;
};
export const dateToIsoString = (date?: Date) => {
  return date ? dayjs(date).format() : undefined;
};

export const formatTimeElapsed = (milliseconds?: number): string => {
  if (!milliseconds) {
    return '--';
  }

  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

export function parseFilterWithDate<T extends Record<string, any>>(filtersStr: string | undefined, dateRangeKeys: (keyof T)[]): Partial<T> {
  if (!filtersStr) {
    return {};
  }

  const parsedObj = JSON.parse(filtersStr) as T;

  for (const key of dateRangeKeys) {
    if (parsedObj[key] && typeof parsedObj[key] === 'string') {
      parsedObj[key] = isoStringToDate(parsedObj[key] as string) as T[keyof T];
    }
  }

  return parsedObj;
}
