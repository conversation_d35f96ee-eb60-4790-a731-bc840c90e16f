/* JenkinsLogViewer.module.scss - Styles for JenkinsLogViewer component */

/* Panel Container */
.panelContainer {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  padding: 16px;
  flex-shrink: 0;
}

/* Body (main flex container for sidebar and console) */
.body {
  display: flex;
  height: 100%;
  min-height: 0;
  flex: 1;
}

/* Stages Section (sidebar) */
.stagesSection {
  flex-shrink: 0;
  background: #f8fafc;
  border-right: 1px solid #e9ecef;
  width: 30%;
}

/* Scroll area for instances */
.instancesScrollArea {
  height: 100%;
}

/* Stage Paper (instance item) */
.stagePaper {
  background: #fff;
  cursor: pointer;
  text-align: center;
  transition: background 0.2s, box-shadow 0.2s;
  border-color: #e9ecef;
}

.stagePaperActive {
  background: #f1f3f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

/* Status text color */
.stageStatus {
  font-size: 0.95em;
  font-weight: 500;
  margin-top: 2px;
}

/* Chevron icon */
.chevron {
  transition: transform 0.2s;
}

.chevronOpen {
  transform: rotate(180deg);
}

/* Console area */
.console {
  width: 70%;
  display: flex;
  flex-direction: column;
  background: #111;
  color: #fff;
}

/* Console header */
.consoleHeader {
  flex-shrink: 0;
  background: #222;
  color: #fff;
  border-bottom: 1px solid #333;
}

.consoleHeaderTitle {
  font-weight: 600;
}

/* Scroll area for log */
.scrollArea {
  flex: 1;
  overflow: auto;
}

/* Log preformatted text */
.logPre {
  background: transparent;
  color: #fff;
  font-family: monospace;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.4;
  min-height: 100%;
}

/* Resize Handle */
.resizeHandle {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  cursor: ns-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.resizeHandle:hover {
  background: #b3b3b3;
}