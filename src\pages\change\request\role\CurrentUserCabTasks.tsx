import React, { useMemo, useState } from 'react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { KanbanAccordion, KanbanButton, KanbanModal, KanbanSelect, KanbanTable, KanbanText, KanbanTextarea } from 'kanban-design-system';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { APPROVAL_ACTION_OPTIONS, ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { ChangeFlowNodeTypeEnum } from '@models/ChangeFlowModel';
import { ChangeRequestRoleUser } from '@core/schema/ChangeRequestRoleUser';
import { statusConfig, WAITING_APPROVAL } from './approval/ChangeRequestApprovalComponent';
import { ChangeRequestApproval, ChangeRequestApprovalStatus, ChangeRequestApprovalStatusEnum } from '@core/schema/ChangeRequestApproval';
import { Badge, Group } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { ChangeRequestTransitionModel } from '@core/schema/ChangeRequest';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import useMutate from '@core/hooks/useMutate';
import { MAX_VALUE_SINGLE_LINE_LENGTH } from '@common/constants/ValidationConstant';
import { useGetCurrentUser } from '@core/hooks/useGetCurrentUser';

interface NodeCabWorkflowTabsProps {
  mode: EntityAction;
  modeApproval: ChangeRequestApprovalMode;
  onRefetchAll?: () => void;
  refetchChangeRequestRoles: () => void;
}

interface ChangeRequestRoleUserTable {
  workflowIdx: number;
  cabGroupIdx: number;
  cabGroupUserIdx: number;
  workflowName: string;
  changeRequestApproval: ChangeRequestApproval;
  username: string;
  changeNodeNamePrevious: string;
  changeRequestApprovalStatusPrevious: ChangeRequestApprovalStatus;
  usernamePrevious: string;
}

const CurrentUserCabTasks: React.FC<NodeCabWorkflowTabsProps> = ({ modeApproval, onRefetchAll, refetchChangeRequestRoles }) => {
  const { control, getValues } = useFormContext<ChangeRequestRoleList>();
  const { fields: roles } = useFieldArray({
    control,
    name: `roles`,
  });
  const { roleIdx } = useMemo(() => {
    const idx = roles.findIndex((role) => ChangeFlowNodeTypeEnum.Enum.CAB === role.changeFlowNode?.type);
    return {
      roleIdx: idx,
    };
  }, [roles]);

  const { fields: workflows } = useFieldArray({
    control,
    name: `roles.${roleIdx}.workflows`,
  });
  const curentUser = useGetCurrentUser().userInfo?.userName;

  const [currentApproval, setCurrentApproval] = useState<ChangeRequestTransitionModel>({
    changeRequestApprovalId: 0,
    status: ChangeRequestApprovalStatusEnum.Enum.TOBE_SEND,
  });
  const [openedApprovalModal, { close: closeApprovalModal, open: openApprovalModal }] = useDisclosure(false);
  const { mutate: approvalRequest } = useMutate(ChangeRequestApi.processChangeRequestApproverTransition, {
    successNotification: `Proccess successfully`,
    errorNotification: (data) => ({ message: data.message }),
    onSuccess: () => {
      closeApprovalModal();
      refetchChangeRequestRoles();
      onRefetchAll?.();
    },
  });
  const handleApproval = () => {
    approvalRequest(currentApproval);
  };
  const [comment, setComment] = useState('');

  const tableData: ChangeRequestRoleUserTable[] = useMemo(() => {
    const rows: ChangeRequestRoleUserTable[] = [];
    workflows.forEach((workflow, workflowIdx) => {
      workflow.groups?.forEach((group, cabGroupIdx) => {
        group.users?.forEach((user, cabGroupUserIdx) => {
          if (user.username === curentUser) {
            const roleUserPath = `roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}.users.${cabGroupUserIdx}` as const;
            const changeRequestRoleUserCurrent = getValues(roleUserPath) as ChangeRequestRoleUser;
            let changeRequestRoleUserPrevious = changeRequestRoleUserCurrent;
            if (cabGroupUserIdx > 0) {
              const roleUserPreviousPath = `roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}.users.${cabGroupUserIdx - 1}` as const;
              changeRequestRoleUserPrevious = getValues(roleUserPreviousPath) as ChangeRequestRoleUser;
            }

            rows.push({
              ...changeRequestRoleUserCurrent,
              workflowIdx: workflowIdx,
              cabGroupIdx: cabGroupIdx,
              cabGroupUserIdx: cabGroupUserIdx,
              workflowName: workflow.changeWorkflowName || '',
              changeRequestApproval: changeRequestRoleUserCurrent.changeRequestApproval || WAITING_APPROVAL,
              username: changeRequestRoleUserCurrent.username || '',
              changeNodeNamePrevious: changeRequestRoleUserPrevious.changeNodeName || '',
              changeRequestApprovalStatusPrevious:
                changeRequestRoleUserPrevious.changeRequestApproval?.overAllStatus || ChangeRequestApprovalStatusEnum.Enum.TOBE_SEND,
              usernamePrevious: changeRequestRoleUserPrevious.username || '',
            });
          }
        });
      });
    });
    return rows;
  }, [workflows, curentUser, getValues, roleIdx]);

  const [openedPanels, setOpenedPanels] = useState<string[]>(() => ['My tasks']);

  if (tableData.length === 0) {
    return null;
  }

  return (
    <Controller
      control={control}
      name={`activeRoles.${roleIdx}`}
      render={() => (
        <KanbanAccordion
          value={openedPanels}
          onChange={(value) => {
            if (!value) {
              return setOpenedPanels([]);
            }
            const newPanels = Array.isArray(value) ? value : [value];
            setOpenedPanels(newPanels);
          }}
          data={[
            {
              key: 'My tasks',
              content: (
                <>
                  <KanbanTable
                    data={tableData}
                    columns={[
                      {
                        name: 'workflow',
                        title: 'Workflow',
                        customRender: (_data, rowData) => <KanbanText>{rowData.workflowName}</KanbanText>,
                      },
                      {
                        name: 'node',
                        title: 'NODE',
                        customRender: (_data, rowData) => <KanbanText>{rowData.changeNodeNamePrevious}</KanbanText>,
                      },
                      {
                        name: 'cab',
                        title: 'CAB phụ thuộc',
                        customRender: (_, rowData) => {
                          const prevStatusConfig = statusConfig[rowData.changeRequestApprovalStatusPrevious];

                          return (
                            <Group gap='xs'>
                              <KanbanText>{rowData.usernamePrevious} - </KanbanText>
                              {prevStatusConfig ? (
                                <Badge
                                  color={prevStatusConfig.color}
                                  radius='xs'
                                  style={{ textTransform: 'none' }}
                                  leftSection={prevStatusConfig.icon}>
                                  {prevStatusConfig.label}
                                </Badge>
                              ) : (
                                <KanbanText>-</KanbanText>
                              )}
                            </Group>
                          );
                        },
                      },

                      {
                        name: 'actions',
                        title: 'Actions',
                        width: '10%',
                        customRender: (_: string, rowData: ChangeRequestRoleUserTable) =>
                          (() => {
                            if (modeApproval !== ChangeRequestApprovalMode.APPROVAL) {
                              return null;
                            }
                            const approvalRequestId = rowData.changeRequestApproval?.id;
                            const isNotActiveAction =
                              !approvalRequestId ||
                              rowData.changeRequestApproval?.overAllStatus !== ChangeRequestApprovalStatusEnum.Enum.PENDING_APPROVAL ||
                              rowData.username !== curentUser;

                            return (
                              <KanbanSelect
                                mb={0}
                                placeholder='Actions'
                                data={APPROVAL_ACTION_OPTIONS}
                                disabled={isNotActiveAction}
                                onChange={(status) => {
                                  if (
                                    status === ChangeRequestApprovalStatusEnum.Enum.ACCEPT ||
                                    status === ChangeRequestApprovalStatusEnum.Enum.REJECT
                                  ) {
                                    setCurrentApproval({
                                      changeRequestApprovalId: Number(approvalRequestId),
                                      status,
                                    });
                                    openApprovalModal();
                                  }
                                }}
                              />
                            );
                          })(),
                      },
                    ]}
                    pagination={{ enable: false }}
                    topBar={{ enable: false }}
                    bottomBar={{ enable: false }}
                  />
                  <KanbanModal
                    title=''
                    size='80%'
                    h='80%'
                    opened={openedApprovalModal}
                    actions={
                      <KanbanButton onClick={handleApproval} disabled={!comment?.trim()}>
                        Confirm
                      </KanbanButton>
                    }
                    onClose={closeApprovalModal}>
                    <KanbanTextarea
                      h='50%'
                      value={comment}
                      onChange={(e) => {
                        const val = e.currentTarget.value;
                        setComment(val);
                        setCurrentApproval((prev) => ({
                          ...prev,
                          approvalComment: val,
                        }));
                      }}
                      maxLength={MAX_VALUE_SINGLE_LINE_LENGTH}
                      placeholder='Enter your comments here...'
                    />
                  </KanbanModal>
                </>
              ),
              title: <KanbanText fw={500}>{'My tasks'}</KanbanText>,
            },
          ]}
        />
      )}
    />
  );
};

export default CurrentUserCabTasks;
