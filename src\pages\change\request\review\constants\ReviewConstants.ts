import { ChangeRequestReviewApprovalStatusEnum } from '@common/constants/ChangeRequestReviewApprovalStatusConstants';
import { CHANGE_REQUEST_REVIEW_STATUS_COLORS, STATUS_COLORS } from '@common/constants/ColorConstant';

// Status color mapping for ReviewTab - using centralized colors
export const reviewStatusBgMap = CHANGE_REQUEST_REVIEW_STATUS_COLORS;

// Approver pill styling utility - using centralized colors
export const getApproverPillProps = (approvalStatus?: string | null) => {
  if (approvalStatus === ChangeRequestReviewApprovalStatusEnum.Enum.ACCEPT) {
    return {
      bg: STATUS_COLORS.ACCEPT.bg,
      color: STATUS_COLORS.ACCEPT.color,
      borderColor: STATUS_COLORS.ACCEPT.borderColor,
    };
  } else if (approvalStatus === ChangeRequestReviewApprovalStatusEnum.Enum.REJECT) {
    return {
      bg: STATUS_COLORS.REJECT.bg,
      color: STATUS_COLORS.REJECT.color,
      borderColor: STATUS_COLORS.REJECT.borderColor,
    };
  } else {
    // Pending status - indigo background to match CAB styling
    return {
      bg: 'var(--mantine-color-indigo-1)',
      c: 'var(--mantine-color-indigo-9)',
      bd: '1px solid var(--mantine-color-indigo-3)',
    };
  }
};
