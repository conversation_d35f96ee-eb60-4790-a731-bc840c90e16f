* {
  box-sizing: border-box;
  /*caunv1: Fix animation block problem from new version mantine core*/
  will-change: auto !important;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

a {
  text-decoration: none;
}

/* Firefox */
/* * {
  scrollbar-width: thin;
  scrollbar-color: #979FB0 #F7F9FF;
} */

/* Chrome, Edge and Safari */
*::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

*::-webkit-scrollbar-track {
  border-radius: 5px;
  background-color: #F7F9FF;
}

*::-webkit-scrollbar-track:hover {
  background-color: #F7F9FF;
}

*::-webkit-scrollbar-track:active {
  background-color: #F7F9FF;
}

*::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: #b9c3d87e;
}

*::-webkit-scrollbar-thumb:hover {
  background-color: #7C85B0;
}

*::-webkit-scrollbar-thumb:active {
  background-color: #7C85B0;
}


.react-grid-placeholder {
  background: var(--mantine-color-blue-1) !important;
  /* Màu nền bóng preview */
  opacity: 0.8 !important;
  /* Độ trong suốt */
  transition: all 0.2s;
}

.react-grid-item.react-grid-item-dragging {
  background-color: rgba(0, 123, 255, 0.2);
  /* Màu nền khi đang kéo thả */
  border: 2px dashed #007bff;
  /* Viền dạng dashed */
}

.ProseMirror {
  height: 100%;
}

.react-flow__node-CHECKLIST_NODE {
  z-index: -1 !important;
}

/*change flow*/
.custom-content-rte-content-100 .ProseMirror {
  min-height: 50px;
}

.custom-content-rte-content-300 .ProseMirror {
  min-height: 300px;
}