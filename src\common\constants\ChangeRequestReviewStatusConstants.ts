// change-request-review-status.ts
import { z } from 'zod';

export const ChangeRequestReviewStatusEnum = z.enum(['IN_PROGRESS', 'SENT_TO_OWNER', 'SENT_TO_APPROVER', 'APPROVED', 'REJECTED']);

export type ChangeRequestReviewStatus = z.infer<typeof ChangeRequestReviewStatusEnum>;

export type StatusOption = {
  label: string;
  value: ChangeRequestReviewStatus;
};

export const statusOptions: StatusOption[] = [
  { label: 'In Progress', value: ChangeRequestReviewStatusEnum.Enum.IN_PROGRESS },
  { label: 'Sent to Owner', value: ChangeRequestReviewStatusEnum.Enum.SENT_TO_OWNER },
  { label: 'Sent to Approver', value: ChangeRequestReviewStatusEnum.Enum.SENT_TO_APPROVER },
  { label: 'Approved', value: ChangeRequestReviewStatusEnum.Enum.APPROVED },
  { label: 'Rejected', value: ChangeRequestReviewStatusEnum.Enum.REJECTED },
];
