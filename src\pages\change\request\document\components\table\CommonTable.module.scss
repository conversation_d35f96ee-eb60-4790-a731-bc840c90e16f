.table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--mantine-radius-md);
  overflow-x: auto;
}
.headerCellCheckBox,
.headerCell {
  width: 20%;
  background-color: var(--mantine-color-indigo-1);
  font-weight: 500;
  vertical-align: middle;
  padding: var(--mantine-spacing-sm);
  border: 1px solid var(--mantine-color-gray-3);
  font-size: var(--mantine-font-size-sm);
  white-space: nowrap;
}

.headerCellCheckBox {
  text-align: center;
}

.headerCell {
  text-align: start;
  min-width: 150px;
}

.cellCenter,
.cell {
  background-color: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  padding: var(--mantine-spacing-sm);
  font-size: var(--mantine-font-size-sm);
  line-height: 1.5;
}

.cell {
  text-align: start;
}

.cellCenter {
  text-align: center;
}

.strike {
  text-decoration: line-through;
}
