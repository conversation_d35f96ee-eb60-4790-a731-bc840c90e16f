import { EntityAction } from '@common/constants/EntityActionConstants';
import * as pathToRegexp from 'path-to-regexp';
import { matchPath, type NavigateFunction, type NavigateOptions, type To } from 'react-router-dom';
import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';

export const ROUTE_PATH = {
  HOME_DASHBOARD: '/',
  CHANGE_REQUEST: '/change-request',
  REPORT: '/report',
  CONFIGURATION: '/configuration',
  CONFIGURATION_USER: '/configuration/system',
  CONFIGURATION_USER_USERS: '/configuration/system/users',
  CONFIGURATION_USER_USERS_DETAIL: '/configuration/system/users/:id',
  CONFIGURATION_USER_ROLES: '/configuration/system/roles',
  CONFIGURATION_USER_ROLE_DETAIL: '/configuration/system/roles/:id',
  CONFIGURATION_TEMPLATE: '/configuration/template',
  CONFIGURATION_USER_GROUP_DETAIL: '/configuration/system/group/:id',
  CONFIGURATION_USER_GROUPS: '/configuration/system/group',
  CONFIGURATION_ACTION: '/configuration/action',
  CONFIGURATION_LIBRARY: '/configuration/library',
  CONFIGURATION_AUDIT: '/configuration/audit',
  CONFIGURATION_NOTIFICATIONS: '/configuration/Notifications',

  CONFIGURATION_CHANGE: '/configuration/change',
  CONFIGURATION_CHANGE_CUSTOM_FIELD: '/configuration/change/custom-field',
  CONFIGURATION_CHANGE_CUSTOM_FIELD_DETAIL: '/configuration/change/custom-field/:id',
  CONFIGURATION_CHANGE_STATUS: '/configuration/change/status',
  CONFIGURATION_CHANGE_STATUS_DETAIL: '/configuration/change/status/:id',
  CONFIGURATION_CHANGE_DOCUMENT: '/configuration/change/document',
  CONFIGURATION_CHANGE_DOCUMENT_DETAIL: '/configuration/change/document/:id',
  CONFIGURATION_CHANGE_CHANGE_TEMPLATE_DETAIL: '/configuration/change/templates/:id',
  CONFIGURATION_CHANGE_CHANGE_TEMPLATE: '/configuration/change/templates',
  CONFIGURATION_CHANGE_FLOW: '/configuration/change/flow',
  CONFIGURATION_CHANGE_FLOW_DETAIL: '/configuration/change/flow/:id',
  CONFIGURATION_CHANGE_WORKFLOW_NODE: '/configuration/change/workflow-nodes',
  CONFIGURATION_CHANGE_WORKFLOW_NODE_DETAIL: '/configuration/change/workflow-nodes/:id',

  CHANGE_REQUEST_DETAIL: '/change-request/:id',
  CHANGE_REQUEST_WORKFLOW_DETAIL: `/change-request/:changeId/workflows/:id`,

  CHANGE_REQUEST_WORKFLOW_EXECUTION_DETAIL: `/change-request/:changeId/execution-detail/:id`,

  CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATES: '/configuration/notifications/email-templates',
  CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATE_DETAIL: '/configuration/notifications/email-templates/:id',
};

export const buildUrl = (routePath: string) => {
  const toPath = pathToRegexp.compile(routePath);
  const originPath = toPath();
  return `${originPath}`;
};

export const buildUrlFromDetail = (routePath: string) => {
  return {
    path: buildUrl(routePath),
    state: buildNavigateState({ fromDetail: true }),
  };
};

export const buildDetailUrl = (routerPath: string, id: number, action: EntityAction) => {
  const toPath = pathToRegexp.compile(routerPath);
  const originPath = toPath({ id });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildChangeDetailUrl = (routerPath: string, changeId: number, id: number, action: EntityAction, tab?: ChangeRquestTabsEnum) => {
  const toPath = pathToRegexp.compile(routerPath);
  const originPath = toPath({ changeId, id });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  if (tab) {
    queryParams.append('tab', tab);
  }
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

type HistoryRouterType = {
  navigate?: NavigateFunction;
};
export const HistoryRouter: HistoryRouterType = {
  navigate: undefined,
};

// export const browserHistory = createBrowserHistory();

export const navigateTo = (to: To, options?: NavigateOptions | undefined) => {
  if (HistoryRouter.navigate) {
    HistoryRouter.navigate(to, options);
  }
};

/**
 *
 * @param aliasPath /ci-types/:id
 * @param realPath localhost/ci-types/4334
 * @returns true
 */
export const isMatchPath = (aliasPath: string, realPath: string) => {
  return !!matchPath(realPath, aliasPath);
};

/**
 *
 * @param aliasPath /ci-types/:id
 * @param realPath /localhost/ci-types/4334
 * @returns `{id: 4334}`
 */
export const parsePathToObject = (aliasPath: string, realPath: string, options?: Parameters<typeof pathToRegexp.pathToRegexp>[2]) => {
  const keys: pathToRegexp.Key[] = [];
  const regexp = pathToRegexp.pathToRegexp(aliasPath, keys, options);

  const match = regexp.exec(realPath);

  const params: Record<string, any> = {};

  if (match) {
    keys.forEach((key, index) => {
      params[key.name] = match[index + 1];
    });
  }
  return params;
};
