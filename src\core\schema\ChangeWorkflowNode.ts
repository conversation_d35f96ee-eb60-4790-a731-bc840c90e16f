import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { ChangeRequestWorkflowNodeTypeEnum } from '@common/constants/ChangeWorkflowNodeType';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { z } from 'zod';

export enum ChangeWorkflowTypeEnum {
  CHECKLIST = 'CHECKLIST',
  IMPLEMENTATION = 'IMPLEMENTATION',
}

// Định nghĩa schema cho ChangeWorkflowNode
export const ChangeWorkflowNodeSchema = z.object({
  id: z.number(),
  nodeId: z.string().optional(),
  nodeName: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(ChangeRequestWorkflowNodeTypeEnum).optional(),
  application: z.nativeEnum(ChangeApplicationTypeEnum).optional(),
  authentication: z.string().nullish(),
  config: z.string().nullish(),
  childrenData: z.string().optional(),
  createdDate: z.string().nullish(),
  createdBy: z.string().nullish(),
  modifiedBy: z.string().nullish(),
  modifiedDate: z.string().nullish(),
});

export type ChangeWorkflowNode = z.infer<typeof ChangeWorkflowNodeSchema>;

export const ChangeWorkflowSchema = z.object({
  id: z.number().optional(),
  name: z.string().trim().nonempty(INPUT_REQUIRE),
  changeId: z.number().optional(),
  workflowData: z.string().optional(),
  type: z.nativeEnum(ChangeWorkflowTypeEnum),
  createdDate: z.string().optional(),
  createdBy: z.string().optional(),
  modifiedBy: z.string().optional(),
  modifiedDate: z.string().optional(),
});

export const ChangeWorkflowExcutionNodeSchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  changeId: z.number().optional(),
  type: z.nativeEnum(ChangeWorkflowTypeEnum),
  workflowData: z.string().optional(),
  createdDate: z.string().optional(),
  createdBy: z.string().optional(),
  modifiedBy: z.string().optional(),
  modifiedDate: z.string().optional(),
  isUpdateNode: z.boolean().optional(),
});

export type ChangeWorkflow = z.infer<typeof ChangeWorkflowSchema>;
