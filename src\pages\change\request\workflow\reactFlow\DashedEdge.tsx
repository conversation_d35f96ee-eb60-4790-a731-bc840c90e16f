import React from 'react';
import { EdgeProps, getBezierPath } from '@xyflow/react';

/**
 * [K-tool gencode][EN] DashedEdge is a custom edge for React Flow, rendered as a dashed SVG path.
 * <AUTHOR> gencode
 * @created_date 2025-07-23
 */
const DashedEdge: React.FC<EdgeProps> = ({ markerEnd, sourcePosition, sourceX, sourceY, style = {}, targetPosition, targetX, targetY }) => {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <g>
      <path
        style={{
          ...style,
          stroke: '#1976d2',
          strokeWidth: 2,
          strokeDasharray: '6,6',
          fill: 'none',
        }}
        className='react-flow__edge-path'
        d={edgePath}
        markerEnd={markerEnd}
      />
    </g>
  );
};

export default DashedEdge;
