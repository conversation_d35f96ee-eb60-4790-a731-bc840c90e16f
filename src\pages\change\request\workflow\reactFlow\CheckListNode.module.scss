// Constants - cập nhật để header to hơn
$header-height: 60px; // Tăng từ 50px lên 60px
$padding: 25px; // Tăng từ 20px lên 25px
$border-radius: 8px;
$transition: 0.2s ease;

.wrapper {
  position: relative;
}

.container {
  position: relative;
  background: var(--mantine-color-white);
  border: 2px solid var(--mantine-color-gray-3);
  border-radius: $border-radius;
  transition: all $transition;
  overflow: visible;

  &:hover:not(.container--selected) {
    border-color: var(--mantine-color-blue-4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &--selected {
    border-color: var(--mantine-color-blue-5);
    box-shadow: 0 0 0 3px var(--mantine-color-blue-2);
  }

  &--collapsed {
    background: linear-gradient(135deg,
        var(--mantine-color-gray-0) 0%,
        var(--mantine-color-blue-0) 100%);
  }

  &--expanded {
    background: var(--mantine-color-white);
    display: flex;
    flex-direction: column;
  }
}

.header {
  display: flex;
  align-items: center;
  padding: 0 $padding;
  gap: var(--mantine-spacing-md); // Tăng từ sm lên md
  user-select: none;
  cursor: pointer;
  transition: background-color $transition;
  border-radius: $border-radius $border-radius 0 0;
  flex-shrink: 0;
  z-index: 10; // Đảm bảo header luôn ở trên child nodes
  position: relative; // Cần để z-index hoạt động

  &--collapsed {
    height: 100%;
    background: transparent;
    justify-content: center;
    border-radius: $border-radius;
  }

  &--expanded {
    height: $header-height; // Sử dụng height mới (60px)
    background: var(--mantine-color-gray-0);
    border-bottom: 2px solid var(--mantine-color-gray-3); // Tăng từ 1px lên 2px
    justify-content: flex-start;

    &:hover {
      background: var(--mantine-color-gray-1);
    }
  }
}

.toggleButton {
  flex-shrink: 0;
  border: 1px solid var(--mantine-color-gray-4);

  &:hover {
    border-color: var(--mantine-color-blue-4);
    background: var(--mantine-color-blue-0);
  }
}

.iconWrapper {
  width: 36px; // Tăng từ 32px lên 36px
  height: 36px; // Tăng từ 32px lên 36px
  border-radius: 8px; // Tăng từ 6px lên 8px
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon {
  width: 24px; // Tăng từ 20px lên 24px
  height: 24px; // Tăng từ 20px lên 24px
  object-fit: contain;
}

.label {
  flex: 1;
  font-weight: 600;
  font-size: var(--mantine-font-size-md); // Tăng từ sm lên md
  color: var(--mantine-color-gray-8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content {
  flex: 1;
  position: relative;
  background: transparent;
  border-radius: 0 0 $border-radius $border-radius;
  min-height: 120px; // Tăng từ 100px lên 120px
  padding-top: 20px; // Thêm padding top để tránh child nodes đè lên header
  z-index: 1; // Đảm bảo content ở dưới header
}

.handle {
  top: 30px;
}

.container:hover .handle,
.container--selected .handle {
  opacity: 1;
}