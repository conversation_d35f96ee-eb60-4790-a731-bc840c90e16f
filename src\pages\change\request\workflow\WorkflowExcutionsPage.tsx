import { ColumnType, KanbanIconButton, KanbanTable, KanbanTableProps, KanbanText, renderDateTime } from 'kanban-design-system';
import React, { useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styles from './WorkflowExcutionsPage.module.scss';
import { ChangeRequestWorkflowApi } from '@api/ChangeRequestWorkflowApi';
import { ChangeRequestWorkflowExecute } from '@core/schema/ChangeRequestWorkflowExecute';
import useFetch from '@core/hooks/useFetch';
import { Flex, Tooltip } from '@mantine/core';
import { IconEye } from '@tabler/icons-react';
import { buildChangeDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';

type ExecutionPageProps = {
  id?: number;
};

/* Reviewed by: K-tool gencode */
const executionColumns: ColumnType<ChangeRequestWorkflowExecute>[] = [
  {
    name: 'id',
    title: 'ID',
    width: '10%',
  },
  {
    name: 'workflowName',
    title: 'Workflow Name',
  },
  {
    name: 'status',
    title: 'Status',
  },
  {
    name: 'startTime',
    title: 'Start Time',
    customRender: renderDateTime,
  },
  {
    name: 'endTime',
    title: 'Finished  Time',
    customRender: (data) => {
      if (data) {
        return renderDateTime(data);
      }
      return '';
    },
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    customRender: renderDateTime,
    hidden: true,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    hidden: true,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
    hidden: true,
  },
  // Add more fields as needed
];

/* Created by: K-tool gencode */
/* Created by: K-tool gencode */
export const WorkflowExcutionsPage: React.FC<ExecutionPageProps> = () => {
  const changeId = Number(useParams().id);
  const navigate = useNavigate();
  const { data: executions } = useFetch(ChangeRequestWorkflowApi.findAll(changeId), { enabled: !!changeId });

  const tableProps: KanbanTableProps<ChangeRequestWorkflowExecute> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeRequestWorkflowExecute> = {
      columns: executionColumns,
      data: executions?.data ?? [],
      actions: {
        customAction: (data) => {
          return (
            <Flex gap='xs' align='center'>
              <Tooltip label='View'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_EXECUTION_DETAIL, changeId, data?.id ?? 0, EntityAction.CREATE));
                  }}>
                  <IconEye />
                </KanbanIconButton>
              </Tooltip>
            </Flex>
          );
        },
      },
    };
    return tblProps;
  }, [executions?.data, changeId, navigate]);

  return (
    <div className={styles.executionPage}>
      {executions?.data?.length !== 0 && <KanbanTable {...tableProps} />}
      {executions?.data?.length === 0 && <KanbanText>No execution yet.</KanbanText>}
    </div>
  );
};
