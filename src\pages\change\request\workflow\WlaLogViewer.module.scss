/* WlaLogViewer.module.scss - Only classes used in WlaLogViewer.tsx */

/* PanelGroup full height */
.panelGroupFullHeight {
  height: 100vh;
}

/* Paper panel */
.paperPanel {
  height: 100%;
  min-height: 0;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
  position: relative;
  resize: none;
}

/* Header */
.header {
  flex-shrink: 0;
}

.title {
  font-size: 1.25rem;
  font-weight: 700;
}

/* Body (main flex container for sidebar and console) */
.body {
  display: flex;
  height: 100%;
  min-height: 0;
  flex: 1;
}

/* Stages Section (sidebar) */
.stagesSection {
  flex-shrink: 0;
  margin-bottom: var(--mantine-spacing-sm);
  background: #f8fafc;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 180px;
  max-width: 320px;
  overflow: hidden;
  width: 30%;
}

/* Scroll area for instances */
.instancesScrollArea {
  flex: 1;
  min-height: 0;
  height: 100%;
  overflow: auto;
}

/* Stage Paper (instance item) */
.stagePaper {
  background: #fff;
  cursor: pointer;
  text-align: center;
  transition: background 0.2s, box-shadow 0.2s;
  border-color: #e9ecef;
}

.stagePaperActive {
  background: #f1f3f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

.stagePaperCustom {
  transition: box-shadow 0.2s;
}

.stagePaperActiveCustom {
  box-shadow: 0 0 0 2px #228be6;
}


/* Status text color */
.stageStatus {
  font-size: 0.95em;
  font-weight: 500;
  margin-top: 2px;
}


/* Chevron icon */
.chevron {
  transition: transform 0.2s;
}

.chevronOpen {
  transform: rotate(180deg);
}

/* Console area */
.console {
  width: 70%;
  min-width: 0;
  display: flex;
  flex-direction: column;
  background: #111;
  color: #fff;
  height: 100%;
  min-height: 0;
  overflow: hidden;
  flex: 1;
}

/* Console header */
.consoleHeader {
  flex-shrink: 0;
  background: #222;
  color: #fff;
  border-bottom: 1px solid #333;
}

.consoleHeaderTitle {
  font-weight: 600;
}

/* Scroll area for log */
.scrollArea {
  flex: 1;
  min-height: 0;
  height: 100%;
  overflow: auto;
}

/* Log preformatted text */
.logPre {
  background: transparent;
  color: #fff;
  font-family: monospace;
  font-size: 14px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.4;
  min-height: 100%;
}

/* Resize Handle */
.resizeHandle {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  cursor: ns-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
  z-index: 10;
  /* Add a visible bar for better UX */
}

.resizeHandle::after {
  content: "";
  width: 40px;
  height: 4px;
  background: #b3b3b3;
  border-radius: 2px;
  margin: 2px 0;
  opacity: 0.7;
}

.resizeHandle:hover {
  background: #b3b3b3;
}