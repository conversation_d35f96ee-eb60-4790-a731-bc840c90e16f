import { z } from 'zod';

export const CiTypeSchema = z.object({
  id: z.number(),
  name: z.string(),
});
export type CiType = z.infer<typeof CiTypeSchema>;

export const AttributeSchema = z.object({
  id: z.number(),
  name: z.string(),
  ciTypeId: z.number(),
  hashId: z.string(),
  group: z.string(),
  type: z.string(),
});
export type Attribute = z.infer<typeof AttributeSchema>;

export const SearchTypeSchema = z.enum(['=', 'LIKE']);
export type SearchType = z.infer<typeof SearchTypeSchema>;
export const SEARCH_TYPES: SearchType[] = SearchTypeSchema.options;

export const AMT_VALUES = {
  IP_SERVER: 'IP SERVER',
  URL: 'URL',
} as const;
export type AmtType = (typeof AMT_VALUES)[keyof typeof AMT_VALUES];
export const AmtTypeSchema = z.enum([AMT_VALUES.IP_SERVER, AMT_VALUES.URL]);
export const AMT_TYPES: AmtType[] = AmtTypeSchema.options;

export const CiInChangeSchema = z.object({
  id: z.number(),
  name: z.string(),
  ciTypeId: z.number().nullish(),
  nameCiType: z.string().nullish(),
  description: z.string().nullish(),
});
export type CiInChange = z.infer<typeof CiInChangeSchema>;

export const CiImpactedSchema = z.object({
  ciChangeId: z.number(),
  ciImpactedId: z.number(),
  ciTypeChangeId: z.number(),
  ciTypeImpactedId: z.number(),
  rootCiId: z.number(),
  ciChangeName: z.string(),
  ciChangeDescription: z.string().nullish(),
  ciTypeChangeName: z.string(),
  ciImpactedName: z.string(),
  ciTypeImpactedName: z.string(),
  rootCiName: z.string(),
});
export type CiImpacted = z.infer<typeof CiImpactedSchema>;

export const CiInformationSchema = z.object({
  cisImpacted: z.array(CiImpactedSchema),
  cisInChange: z.array(CiInChangeSchema),
});
export type CiInformationType = z.infer<typeof CiInformationSchema>;

export const SERVICE_AFFECTED = 'System Group'; // Mock value for testing
export const MAX_LENGTH_CI_IMPACTED = 5;

export const ModeCI = {
  CI_IMPACTED: 'CIs Impacted',
  SERVICE_AFFECTED: 'Service Affected',
} as const;
export type ModeCIType = (typeof ModeCI)[keyof typeof ModeCI];
