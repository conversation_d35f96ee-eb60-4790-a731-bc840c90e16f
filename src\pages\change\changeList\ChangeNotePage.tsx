import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { getDefaultTableAffected, KanbanText, KanbanTextarea, renderDateTime } from 'kanban-design-system';
import { IconNote } from '@tabler/icons-react';
import useFetch from '@core/hooks/useFetch';
import { useDisclosure } from '@mantine/hooks';
import useMutate from '@core/hooks/useMutate';
import customStyled from '@pages/change/changeList/components/ChangeNote.module.scss';
import commonStyled from '@resources/styles/Common.module.scss';
import { Box, Paper, Stack, ThemeIcon } from '@mantine/core';
import { ChangeRequestNote } from '@core/schema/ChangeRequestNote';
import { ChangeStageType } from '@common/constants/ChageStageType';
import NoteCard from '@pages/change/changeList/components/NoteCard';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { MAX_VALUE_MULTI_LINE_LENGTH } from '@common/constants/ValidationConstant';
import { useGetCurrentUser } from '@core/hooks/useGetCurrentUser';

export type ChangeNoteRef = {
  handleCancel: () => void;
  handleSave: () => void;
};
interface ChangeNoteProps {
  changeId: number;
  stage: string;
  onCancelModal: () => void;
  onContentChange: (isEmpty: boolean) => void;
}

const ChangeNotePage = forwardRef<ChangeNoteRef, ChangeNoteProps>(function ChangeNotePage({ changeId, onCancelModal, onContentChange, stage }, ref) {
  const [noteContent, setNoteContent] = useState<string>('');
  const [openedModalConfirmCancelMulti, { close: closeModalConfirmCancelMulti, open: openModalCancel }] = useDisclosure(false);
  const currentUser = useGetCurrentUser();

  const { data: changeNotesResponse } = useFetch(ChangeRequestApi.findAllNote(getDefaultTableAffected(), changeId));
  const listData = changeNotesResponse?.data?.content || [];
  const { mutate: saveNote } = useMutate(ChangeRequestApi.upsertNote, {
    successNotification: 'Note saved successfully',
    onSuccess: () => {
      onCancelModal();
    },
  });

  useImperativeHandle(ref, () => ({
    handleCancel,
    handleSave,
  }));
  const handleCancel = () => {
    if (noteContent.trim()) {
      openModalCancel();
    } else {
      setNoteContent('');
      onCancelModal();
    }
  };

  const handleSave = () => {
    if (!noteContent.trim()) {
      return;
    }
    const payload: ChangeRequestNote = {
      id: 0,
      changeRequestId: changeId,
      content: noteContent.trim(),
      createdBy: currentUser?.userInfo?.name || '',
      createdDate: new Date().toISOString(),
      modifiedDate: new Date().toISOString(),
      stage: (stage as ChangeStageType) ?? undefined,
    };

    saveNote(payload);
  };

  return (
    <Box>
      <Box maw={800} mx='auto' p='sm' bg='white' pos='relative' className={commonStyled.elementBorder}>
        <Stack mah={200} display='flex' className={customStyled.listAreaStyle}>
          {listData && listData.length > 0 ? (
            listData.map((note) => <NoteCard key={note.id} note={note} />)
          ) : (
            <KanbanText>No Notes present</KanbanText>
          )}
        </Stack>
      </Box>

      <Paper className={customStyled.noteStyle} display='flex' withBorder shadow='xs' bg='white' maw={800} radius='sm'>
        <Box bg='primary.3' c='dark.6' fw='bold' fz='sm' px='sm' py='xs' display='flex' className={customStyled.headerStyle}>
          <ThemeIcon color='primary.3'>
            <IconNote color='white' size={16} fill='var(--mantine-color-yellow-5)' />
          </ThemeIcon>
          <KanbanText size='sm' c='white'>
            {currentUser?.userInfo?.userName}
          </KanbanText>
          <KanbanText fw='normal' size='xs' c='white' ml={8}>
            said on {renderDateTime(new Date())}
          </KanbanText>
        </Box>
        <Box p='sm' fz='sm' style={{ whiteSpace: 'pre-wrap' }}>
          <KanbanTextarea
            value={noteContent}
            maxLength={MAX_VALUE_MULTI_LINE_LENGTH}
            onChange={(e) => {
              setNoteContent(e.target.value);
              onContentChange(e.target.value.trim() === '');
            }}
            styles={{
              input: {
                height: '200px',
                border: 'none',
                background: 'white',
                padding: 0,
              },
            }}
            placeholder='Add a new note...'
          />
        </Box>
      </Paper>

      <UnsaveConfirmModal
        opened={openedModalConfirmCancelMulti}
        onClose={closeModalConfirmCancelMulti}
        onConfirm={() => {
          closeModalConfirmCancelMulti();
          onCancelModal();
        }}
      />
    </Box>
  );
});
export default ChangeNotePage;
