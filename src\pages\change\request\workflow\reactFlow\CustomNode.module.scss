.nodeWrapper {
  position: relative;
  display: inline-block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.12));

  &:hover {
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.16));

    .connectionHints {
      opacity: 1;
    }

    .handle {
      opacity: 1;
      transform: scale(1.1);
    }
  }
}

.customNode {
  position: relative;
  width: 160px;
  height: 160px;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 16px 12px 12px 12px;
  cursor: pointer;
  overflow: hidden;

  // Subtle inner shadow for depth
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-2px);
    border-color: #cbd5e1;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  }


  &.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    transform: translateY(-2px);

    .connectionHints {
      opacity: 1;
    }

    .handle {
      opacity: 1;
    }

    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: inherit;
      background: linear-gradient(45deg, #3b82f6, #1d4ed8);
      z-index: -1;
      opacity: 0.1;
    }
  }

  &.error {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);

    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: inherit;
      background: linear-gradient(45deg, #ef4444, #dc2626);
      z-index: -1;
      opacity: 0.1;
    }
  }

  // Running state styles
  &.running {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    animation: runningGlow 2s ease-in-out infinite alternate;

    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: inherit;
      background: linear-gradient(45deg, #f59e0b, #d97706);
      z-index: -1;
      opacity: 0.15;
    }

    .connectionHints {
      opacity: 0.5;
    }

    .handle {
      opacity: 0.7;
      pointer-events: none;
    }
  }
}

// Running Ring Animation
.runningRing {
  position: absolute;
  top: 28%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85px;
  height: 85px;
  z-index: 3;
  pointer-events: none;
}



.iconContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  z-index: 4;
  transition: all 0.3s ease;

  .customNode.selected & {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-color: #93c5fd;
  }

  .customNode.error & {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-color: #fca5a5;
  }

  .customNode.running & {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f59e0b;
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  }

  &.runningIcon {
    animation: iconPulse 2s ease-in-out infinite;
  }
}

.runningPulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #f59e0b;
  border-radius: 16px;
  animation: pulse 2s ease-in-out infinite;
  opacity: 0.5;
}

.nodeIcon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;

  .customNode.running & {
    filter: drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3));
  }
}

.contentContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 4px;
  z-index: 2;
  width: 100%;
}

.nodeLabel {
  color: #1e293b;
  font-weight: 600;
  text-align: center;
  word-break: break-word;
  line-height: 1.3;

  .customNode.selected & {
    color: #1e40af;
  }

  .customNode.error & {
    color: #dc2626;
  }

  .customNode.running & {
    color: #d97706;
  }
}

.nodeSubtitle {
  color: #64748b;
  text-align: center;
  line-height: 1.2;

  .customNode.selected & {
    color: #3730a3;
  }

  .customNode.error & {
    color: #b91c1c;
  }

  .customNode.running & {
    color: #92400e;
  }
}

.runningText {
  color: #d97706;
  font-weight: 500;
  animation: textPulse 1.5s ease-in-out infinite;
  margin-top: 2px;
}

.errorIcon {
  position: absolute;
  top: 8px;
  right: 8px;
}

.logIcon {
  position: absolute;
  top: 8px;
  left: 8px;
}

.errorTooltip {
  max-width: 200px;
  white-space: pre-line;
}

.statusIndicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  z-index: 3;

  &.successStatus {
    background: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }

  &.errorStatus {
    background: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
    animation: pulse 2s infinite;
  }

  &.runningStatus {
    background: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
    animation: runningPulse 1s ease-in-out infinite;
  }
}

// Connection Handles
.handle {
  background: #ffffff;
  width: 16px;
  height: 16px;
  border: 3px solid #6b7280;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 5;
  opacity: 0;
  cursor: crosshair;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &:hover {
    transform: translateY(-50%) scale(1.25);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  // Target handle (for incoming connections - Left side)
  &.targetHandle {
    border-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);

    &:hover {
      border-color: #059669;
      background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
      box-shadow: 0 0 0 6px rgba(16, 185, 129, 0.2), 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    .customNode.selected & {
      border-color: #10b981;
      background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    }

    .customNode.error & {
      border-color: #f59e0b;
      background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    }

    .customNode.running & {
      border-color: #f59e0b;
      background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
      opacity: 0.7;
    }
  }

  // Source handle (for outgoing connections - Right side)
  &.sourceHandle {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);

    &:hover {
      border-color: #2563eb;
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
      box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.2), 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    .customNode.selected & {
      border-color: #3b82f6;
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    }

    .customNode.error & {
      border-color: #ef4444;
      background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    }

    .customNode.running & {
      border-color: #f59e0b;
      background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
      opacity: 0.7;
    }
  }

  // Position-specific handles
  &.leftHandle {
    left: -8px;
  }

  &.rightHandle {
    right: -8px;
  }
}

// Connection Hints
.connectionHints {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 4;
}

.connectionHint {
  position: absolute;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  transform: translateY(-50%);

  &.leftHint {
    left: -12px;
  }

  &.rightHint {
    right: -12px;
  }
}

.hintDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #94a3b8;
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 2px rgba(148, 163, 184, 0.3);
}

@keyframes runningPulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

// Animations - Cập nhật và thêm mới
@keyframes iconSpin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes iconPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes iconPulseEffect {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.4;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.2;
  }
}

@keyframes iconGlow {

  0%,
  100% {
    filter: drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3));
  }

  50% {
    filter: drop-shadow(0 4px 8px rgba(245, 158, 11, 0.5));
  }
}

@keyframes runningGlow {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
  }

  50% {
    box-shadow: 0 0 30px rgba(245, 158, 11, 0.4);
  }
}

@keyframes runningPulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes textPulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}