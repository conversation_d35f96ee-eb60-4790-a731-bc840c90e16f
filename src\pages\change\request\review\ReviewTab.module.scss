/* Owner Pill Styles - Using Mantine color system */
.ownerPill {
  &.disabled {
    background-color: var(--mantine-color-gray-1);
    color: var(--mantine-color-gray-6);
  }

  &.enabled {
    background-color: var(--mantine-color-blue-0);
    color: inherit;
  }
}

/* Document Button Styles - Using <PERSON><PERSON>'s disabled state */
.documentButton {
  width: 100%;
  min-width: fit-content;
  white-space: normal;
  height: auto;
  min-height: 32px;
  padding: 6px 12px;

  &.disabled {
    background-color: var(--mantine-color-gray-0);
  }

  &.enabled {
    background-color: var(--mantine-color-blue-6);
  }
}

/* Document Button Text Styles */
.documentButtonText {
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  line-height: 1.4;
  display: block;
  width: 100%;
}

/* Document Container - Ensures proper spacing and layout matching Documents tab */
.documentContainer {
  width: 100%;
  min-width: 0; /* Allows flex shrinking */
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Approver MultiSelect Styles - Using Mantine size system */
.approverMultiSelect {
  /* Width handled by w="100%" prop in component */

  :global(.mantine-MultiSelect-input) {
    font-size: 12px;
  }
}

/* Approver ComboboxLoadMore Styles - Using Mantine spacing and typography */
.approverComboboxLoadMore {
  /* Width handled by w="100%" prop in component */

  :global(.mantine-PillsInput-input) {
    font-size: 14px;
  }

  :global(.mantine-Combobox-dropdown) {
    max-height: 200px;
  }

  /* Pill spacing using Mantine spacing system */
  :global(.mantine-PillsInput-field) {
    gap: 2px;
    row-gap: 2px;
  }

  :global(.mantine-Pill-root) {
    margin-bottom: 2px;
    font-size: 14px;
    background-color: var(--mantine-color-indigo-1);
    color: var(--mantine-color-indigo-9);
    border: 1px solid var(--mantine-color-indigo-3);
  }

  :global(.mantine-Pill-label) {
    font-size: 14px;
  }
}

/* No Approvers Input Styles - Using Mantine typography system */
.noApproversInput {
  /* Width handled by w="100%" prop in component */

  :global(.mantine-Input-input) {
    font-size: 12px;
  }
}

/* Note Section Styles - Using Mantine color system */
.noteSection {
  background-color: var(--mantine-color-white);
  border-color: var(--mantine-color-gray-3);
}

.noteTitle {
  /* Margin handled by mb={0} prop in component */
  color: var(--mantine-color-dark-7);
  font-weight: var(--mantine-font-weight-semibold);
}

.noteTextarea {
  :global(.mantine-Textarea-input) {
    font-size: 14px;
  }
}

/* Modal Textarea Styles - Using Mantine spacing and color system */
.modalTextarea {
  :global(.mantine-Textarea-input) {
    font-size: 14px;
    border-radius: var(--mantine-radius-sm);
    border: 1px solid var(--mantine-color-gray-4);

    &:focus {
      border-color: var(--mantine-color-blue-6);
      box-shadow: 0 0 0 1px var(--mantine-color-blue-6);
    }
  }
}

/* Approval Modal Styles - Using Mantine radius system */
.approvalModal {
  :global(.mantine-Modal-content) {
    border-radius: var(--mantine-radius-md);
  }
}

.modalTitle {
  color: var(--mantine-color-dark-9);
}

.modalCloseButton {
  color: var(--mantine-color-gray-6);

  &:hover {
    background-color: var(--mantine-color-gray-1);
  }
}

/* Custom Alert Styling - Using Mantine color and radius system */
.approvalAlert {
  background-color: var(--mantine-color-yellow-0) !important;
  border: 1px solid var(--mantine-color-yellow-4) !important;
  border-radius: var(--mantine-radius-sm);

  :global(.mantine-Alert-icon) {
    color: var(--mantine-color-orange-6);
  }

  :global(.mantine-Alert-body) {
    color: var(--mantine-color-dark-7);
  }
}

.commentLabel {
  color: var(--mantine-color-dark-8);
}

/* Status Badge Styles - Using Mantine spacing and typography system */
.statusBadge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: var(--mantine-radius-xl);
  font-size: 12px;
  font-weight: var(--mantine-font-weight-medium);
  border: 1px solid;
}
