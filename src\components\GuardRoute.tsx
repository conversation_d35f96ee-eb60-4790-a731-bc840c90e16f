import React from 'react';
import ForbiddenPage from '@pages/base/ForbiddenPage';
import { AclPermission } from '@core/schema/AclPermission';
import { ProtectedRoute } from '@core/auth/hocs/ProtectedRoute';
import { useGetCurrentUser } from '@core/hooks/useGetCurrentUser';

//ft/role router authorize
export type GuardRouteType = {
  requirePermissions: AclPermission[];
  children: React.ReactNode;
  allMatchPermissions?: boolean;
};

const GuardRoute: React.FC<GuardRouteType> = (props: GuardRouteType) => {
  const currentUser = useGetCurrentUser().userInfo;
  return (
    <ProtectedRoute
      errorElement={<ForbiddenPage />}
      requirePermissions={props.requirePermissions}
      userPermissions={currentUser?.aclPermissions || []}
      allMatchPermissions={props.allMatchPermissions}>
      {props.children}
    </ProtectedRoute>
  );
};

export default GuardRoute;
