import { ChangeRequestReviewStatusEnum } from '@common/constants/ChangeRequestReviewStatusConstants';
import { ChangeRequestReviewApproverSchema } from '@core/schema/ChangeRequestReview';
import { z } from 'zod';

export const ReviewFormValuesSchema = z.object({
  owner: z.string().optional(),
  ownerFullName: z.string().optional(),
  approvers: z.array(ChangeRequestReviewApproverSchema).optional(),
  status: ChangeRequestReviewStatusEnum,
  lastestComment: z.string().optional(),
  note: z.string().optional(),
  documentName: z.string().optional(),
  action: z.string().nullish(),
});

export type ReviewFormValues = z.infer<typeof ReviewFormValuesSchema>;
