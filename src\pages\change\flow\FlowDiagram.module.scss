/* ReactFlowFix.module.scss */
.flowCustomEdge {

  /* Trong FlowDiagram.module.scss */
  :global(.react-flow__edges) {
    overflow: visible;
    /* Đ<PERSON>m bảo overflow là visible */
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    /* <PERSON><PERSON><PERSON> bảo SVG rộng hơn */
    height: 95vh;
    /* Đảm bảo SVG cao hơn */
  }

  :global(.react-flow__edges svg) {
    overflow: visible;
    width: 95vw;
    /* Đ<PERSON>m bảo SVG rộng hơn */
    height: 95vh;
    /* <PERSON><PERSON><PERSON> bảo SVG cao hơn */
  }
}