.table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--mantine-radius-md);
  overflow-x: auto;
  width: 100%;
  table-layout: fixed;
}
.headerCellCheckBox,
.headerCell {
  background-color: var(--mantine-color-blue-0);
  font-weight: 500;
  text-align: start;
  vertical-align: middle;
  padding: var(--mantine-spacing-sm);
  border: 1px solid var(--mantine-color-gray-3);
  font-size: var(--mantine-font-size-sm);
  white-space: nowrap;
}

.headerCell {
  min-width: 150px;
}

.cellCenter,
.cell {
  background-color: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  padding: var(--mantine-spacing-sm);
  font-size: var(--mantine-font-size-sm);
  line-height: 1.5;
  min-height: 60px;
  vertical-align: top;
}

.cell {
  text-align: start;
  display: table-cell;
  vertical-align: top;
}

.cellCenter {
  text-align: center;
  display: table-cell;
  vertical-align: top;
}

.strike {
  text-decoration: line-through;
}

/* Consistent disabled styling for form elements */
.cell input:disabled,
.cell select:disabled,
.cell textarea:disabled,
.cell button:disabled {
  opacity: 0.7;
  background-color: var(--mantine-color-gray-0);
  color: var(--mantine-color-gray-6);
  cursor: not-allowed;
}

.cell .mantine-Select-input:disabled,
.cell .mantine-MultiSelect-input:disabled,
.cell .mantine-Textarea-input:disabled {
  opacity: 0.7;
  background-color: var(--mantine-color-gray-0);
  color: var(--mantine-color-gray-6);
}

/* Disabled pill styling */
.cell .mantine-Pill-root[data-disabled="true"] {
  opacity: 0.7;
  background-color: var(--mantine-color-gray-1);
  color: var(--mantine-color-gray-6);
}

/* Column width classes for ReviewTab */
.ownerColumn {
  width: 15%;
  min-width: 120px;
}

.documentColumn {
  width: 15%;
  min-width: 150px;
  max-width: 300px;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  overflow-wrap: break-word;
  hyphens: auto;
}

.approverColumn {
  width: 30%;
  min-width: 200px;
}

.statusColumn {
  width: 12%;
  min-width: 100px;
}

.commentColumn {
  width: 28%;
  min-width: 180px;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 1200px) {
  .ownerColumn {
    width: 18%;
    min-width: 110px;
  }

  .documentColumn {
    width: 18%;
    min-width: 130px;
    max-width: 250px;
  }

  .approverColumn {
    width: 28%;
    min-width: 180px;
  }

  .statusColumn {
    width: 14%;
    min-width: 90px;
  }

  .commentColumn {
    width: 22%;
    min-width: 160px;
  }
}

@media (max-width: 768px) {
  .table {
    table-layout: auto;
  }

  .ownerColumn,
  .documentColumn,
  .approverColumn,
  .statusColumn,
  .commentColumn {
    width: auto;
    min-width: 100px;
  }
}
