import { ChangeRequestReviewStatusEnum } from '@common/constants/ChangeRequestReviewStatusConstants';
import { COMMON_MAX_LENGTH, NOTE_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ReviewFormValues } from '../ReviewFormValues';

// Helper function to validate required owner and note fields
const validateOwnerAndNote = (trimmedOwner: string, trimmedNote: string): boolean => {
  return !(!trimmedOwner || !trimmedNote);
};

// Helper function to validate field length constraints
const validateFieldLengths = (trimmedOwner: string, trimmedNote: string): boolean => {
  return !(trimmedOwner.length > COMMON_MAX_LENGTH || trimmedNote.length > NOTE_MAX_LENGTH);
};

// Helper function to validate document requirement
const validateDocument = (selectedFile: File | null, documentName?: string): boolean => {
  return !!(selectedFile || documentName);
};

// Helper function to validate approvers requirement and length constraints
const validateApprovers = (validApprovers: any[]): boolean => {
  if (validApprovers.length === 0) {
    return false;
  }
  const hasInvalidApprover = validApprovers.some((approver) => (approver.approver?.trim().length || 0) > COMMON_MAX_LENGTH);
  return !hasInvalidApprover;
};

export const validateBeforeSave = (review: ReviewFormValues, selectedFile: File | null, isOwner: boolean, isChangeCoordinator: boolean): boolean => {
  const trimmedOwner = review.owner?.trim() || '';
  const trimmedNote = review.note?.trim() || '';
  const validApprovers = review.approvers?.filter((approver) => approver.approver?.trim()) || [];

  // Validation for IN_PROGRESS status
  if (review.status === ChangeRequestReviewStatusEnum.Enum.IN_PROGRESS) {
    return validateOwnerAndNote(trimmedOwner, trimmedNote) && validateFieldLengths(trimmedOwner, trimmedNote);
  }
  // Validation for other statuses (SENT_TO_OWNER, SENT_TO_APPROVER, etc.)
  else {
    // Owner role validation - requires all fields
    if (isOwner) {
      return (
        validateOwnerAndNote(trimmedOwner, trimmedNote) &&
        validateFieldLengths(trimmedOwner, trimmedNote) &&
        validateDocument(selectedFile, review.documentName) &&
        validateApprovers(validApprovers)
      );
    }
    // Change Coordinator role validation - requires only owner and note
    else if (isChangeCoordinator) {
      return validateOwnerAndNote(trimmedOwner, trimmedNote) && validateFieldLengths(trimmedOwner, trimmedNote);
    }
  }

  // Default case - invalid if no matching role/status combination
  return false;
};

export const validateSentToOwner = (review: ReviewFormValues): boolean => {
  const trimmedOwner = review.owner?.trim() || '';
  const trimmedNote = review.note?.trim() || '';

  return validateOwnerAndNote(trimmedOwner, trimmedNote) && validateFieldLengths(trimmedOwner, trimmedNote);
};

export const validateSentToApprover = (review: ReviewFormValues, selectedFile: File | null): boolean => {
  const trimmedOwner = review.owner?.trim() || '';
  const trimmedNote = review.note?.trim() || '';
  const validApprovers = review.approvers?.filter((approver) => approver.approver?.trim()) || [];

  return (
    validateOwnerAndNote(trimmedOwner, trimmedNote) &&
    validateFieldLengths(trimmedOwner, trimmedNote) &&
    validateDocument(selectedFile, review.documentName) &&
    validateApprovers(validApprovers)
  );
};
