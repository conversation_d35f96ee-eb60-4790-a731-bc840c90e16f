import { Box, Text } from '@mantine/core';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import React from 'react';
import styles from './StartNode.module.scss';
import { NodeData } from './CustomNode';
import clsx from 'clsx';
import { IconPlayerPlay, IconPlayerPause } from '@tabler/icons-react';

interface StartNodeProps {
  id: string;
  data: NodeData;
  selected?: boolean;
}

function StartNode({ data, selected }: StartNodeProps) {
  const nodeClass = clsx(styles.startNode, selected && styles.selected, data.isRunning && styles.running);

  const iconClass = clsx(styles.icon, data.isRunning && styles.runningIcon);
  const handleClass = clsx(styles.handle, data.isRunning && styles.runningHandle);

  return (
    <Box className={nodeClass} aria-label={data.label || 'Start node'}>
      {/* Header: icon + title */}
      <Box className={styles.header}>
        <Box className={styles.iconWrap} aria-hidden='true' title={data.isRunning ? 'Pause' : 'Start'}>
          <span className={iconClass}>{data.isRunning ? <IconPlayerPause size={16} stroke={1.8} /> : <IconPlayerPlay size={16} stroke={1.8} />}</span>
        </Box>

        <Text className={styles.title}>{data.label || 'Start'}</Text>
      </Box>

      {/* Body: status */}
      <Box className={styles.body}>
        <Text className={styles.subtleHint}>Start</Text>
      </Box>

      {/* Ports (React Flow handles) */}
      <Handle type='source' position={Position.Right} className={clsx(handleClass, styles.handleOut)} id='source-right' />
    </Box>
  );
}

export default StartNode;
