import React, { useCallback } from 'react';
import { Stack, Flex, ComboboxData } from '@mantine/core';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import { CustomNodeData } from '@pages/change/request/workflow/reactFlow/CustomNode';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import styles from './ParametersTab.module.scss';
import JenkinsParametersForm from './JenkinsParametersForm';
import JenkinsBrowseJobsModal from './JenkinsBrowseJobsModal';
import { klona } from 'klona';

interface JenkinsParameter {
  name: string;
  type: string;
  defaultValue: string | number | boolean | null;
  description?: string;
  choices?: string[];
}

type JenkinsParametersTabProps = {
  customNodeData: CustomNodeData | undefined;
  setCustomNodeData: (data: React.SetStateAction<CustomNodeData | undefined>) => void;
};

const operationOptions: ComboboxData = [{ value: 'build', label: 'Build' }];

export const JenkinsParametersTab: React.FC<JenkinsParametersTabProps> = ({ customNodeData, setCustomNodeData }) => {
  const handleOperationChange = useCallback(
    (value: string | null) => {
      setCustomNodeData((prev) => {
        if (!prev) {
          return prev;
        }

        const updated = klona(prev);
        updated.parameters = {
          ...updated.parameters,
          operation: value || '',
          jobPath: '',
          jenkinsParams: [],
          jenkinsParamValues: {},
        };
        return updated;
      });
    },
    [setCustomNodeData],
  );

  const handleJobPathChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      setCustomNodeData((prev) => {
        if (!prev) {
          return prev;
        }

        const updated = klona(prev);
        updated.parameters = {
          ...updated.parameters,
          jobPath: value,
          jenkinsParams: [],
          jenkinsParamValues: {},
        };
        return updated;
      });
    },
    [setCustomNodeData],
  );

  const handleJobSelect = useCallback(
    (jobUrl: string, parameters: JenkinsParameter[]) => {
      setCustomNodeData((prev) => {
        if (!prev) {
          return prev;
        }

        const updated = klona(prev);
        updated.parameters = {
          ...updated.parameters,
          jobPath: jobUrl,
          jenkinsParams: parameters,
          jenkinsParamValues: {},
        };
        return updated;
      });
    },
    [setCustomNodeData],
  );

  const handleParameterChange = useCallback(
    (name: string, value: string | number | boolean) => {
      setCustomNodeData((prev) => {
        if (!prev) {
          return prev;
        }

        const updated = klona(prev);
        updated.parameters = updated.parameters || {};
        updated.parameters.jenkinsParamValues = updated.parameters.jenkinsParamValues || {};
        updated.parameters.jenkinsParamValues[name] = value;

        return updated;
      });
    },
    [setCustomNodeData],
  );

  const selectedOperation = customNodeData?.parameters?.operation || '';
  const selectedJobPath = customNodeData?.parameters?.jobPath || '';
  const jenkinsParams = customNodeData?.parameters?.jenkinsParams || [];
  const jenkinsParamValues = customNodeData?.parameters?.jenkinsParamValues;

  return (
    <Stack gap='md'>
      <KanbanSelect
        required
        label='Operation'
        placeholder='Select operation'
        data={operationOptions}
        value={selectedOperation}
        onChange={handleOperationChange}
        clearable={false}
      />

      {selectedOperation && (
        <Flex align='end' gap='sm' className={styles.jobUrlInputWrapper}>
          <KanbanInput
            required
            label='Job URL'
            placeholder='Select a job from the tree or enter URL manually'
            value={selectedJobPath}
            onChange={handleJobPathChange}
            maxLength={500}
            style={{ flex: 1 }}
          />
          <JenkinsBrowseJobsModal
            changeNodeId={customNodeData?.changeNodeId}
            application={customNodeData?.application}
            onJobSelect={handleJobSelect}
            disabled={!customNodeData?.changeNodeId || customNodeData.application !== ChangeApplicationTypeEnum.JENKINS}
          />
        </Flex>
      )}

      {selectedOperation && selectedJobPath && jenkinsParams.length > 0 && (
        <div className={styles.paramsCard}>
          <h4 className={styles.paramsTitle}>Job Parameters</h4>
          <JenkinsParametersForm parameters={jenkinsParams} values={jenkinsParamValues} onChange={handleParameterChange} />
        </div>
      )}
    </Stack>
  );
};

export default JenkinsParametersTab;
