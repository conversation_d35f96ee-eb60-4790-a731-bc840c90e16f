import React from 'react';
import { KanbanButton } from 'kanban-design-system';
import { ChangeRequestWorkflowApi } from '@api/ChangeRequestWorkflowApi';
import useMutate from '@core/hooks/useMutate';
import { Group } from '@mantine/core';

type RunManualTabProps = {
  changeId: number;
  workflowId: number;
  nodeId: string;
};

export enum ConfirmStatusManualNodeEnum {
  SUCCESS = 'SUCCESS',
  FAILURE = 'FAILURE',
}

export const RunNodeManualButton: React.FC<RunManualTabProps> = ({ changeId, nodeId, workflowId }) => {
  //update status node run workflow
  const { mutate: runNodeMutate } = useMutate(ChangeRequestWorkflowApi.executionNode, {});
  const { mutate: updateStatus } = useMutate(ChangeRequestWorkflowApi.updateStatus, {});

  return (
    <Group grow>
      <KanbanButton
        onClick={() => {
          const status = ConfirmStatusManualNodeEnum.SUCCESS;
          updateStatus({ changeId, workflowId, nodeId, status });
        }}>
        Success
      </KanbanButton>

      <KanbanButton
        onClick={() => {
          const status = ConfirmStatusManualNodeEnum.FAILURE;
          updateStatus({ changeId, workflowId, nodeId, status });
        }}>
        Confirm
      </KanbanButton>

      <KanbanButton
        onClick={() => {
          runNodeMutate({ changeId, workflowId, nodeId });
        }}>
        Run
      </KanbanButton>
    </Group>
  );
};

export default RunNodeManualButton;
