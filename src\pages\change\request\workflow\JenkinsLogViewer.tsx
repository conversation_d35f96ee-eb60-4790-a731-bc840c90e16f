import React, { useState, useRef, useEffect } from 'react';
import { Paper, Flex, Group, Text, Box, Tooltip, ScrollArea, Stack, Title } from '@mantine/core';
import {
  IconPlayerPause,
  IconCheck,
  IconX,
  IconClock,
  IconTerminal2,
  IconChevronDown,
  IconBan,
  IconPlayerSkipBack,
  IconAlertCircle,
} from '@tabler/icons-react';
import { KanbanIconButton } from 'kanban-design-system';
import styles from './JenkinsLogViewer.module.scss'; // Import SCSS module
import type { Node } from '@xyflow/react';
import useRegisterWebsocketListener from '@core/hooks/useRegisterWebsocketListener';
import { JenkinsStage, JenkinsStageStatusEnum, WebsocketDataTypeEnum } from '@core/schema/WebsocketData';
import { formatTimeElapsed } from '@common/utils/DateUtils';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

type JenkinsLogViewerProps = {
  node: Node;
  onClose: () => void;
};

// Map Jenkins status to icons
const statusIcon: Record<JenkinsStageStatusEnum, JSX.Element> = {
  [JenkinsStageStatusEnum.SUCCESS]: <IconCheck color='green' size={18} />,
  [JenkinsStageStatusEnum.FAILED]: <IconX color='red' size={18} />,
  [JenkinsStageStatusEnum.PENDING]: <IconClock color='gray' size={18} />,
  [JenkinsStageStatusEnum.IN_PROGRESS]: <IconClock color='blue' size={18} />,
  [JenkinsStageStatusEnum.ABORTED]: <IconBan color='orange' size={18} />,
  [JenkinsStageStatusEnum.SKIPPED]: <IconPlayerSkipBack color='gray' size={18} />,
  [JenkinsStageStatusEnum.UNSTABLE]: <IconAlertCircle color='yellow' size={18} />,
  [JenkinsStageStatusEnum.PAUSED]: <IconPlayerPause color='cyan' size={18} />,
  [JenkinsStageStatusEnum.CANCELLED]: <IconX color='red' size={18} />,
};

export const JenkinsLogViewer: React.FC<JenkinsLogViewerProps> = ({ node, onClose }) => {
  const [opened, setOpened] = useState<number | null>(null);
  const [log, setLog] = useState<string>('');
  const [stages, setStages] = useState<JenkinsStage | undefined>();

  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useRegisterWebsocketListener((message) => {
    if (message.type === WebsocketDataTypeEnum.JENKIN_LOG) {
      setLog(message?.content ?? '');
    }
    if (message.type === WebsocketDataTypeEnum.JENKIN_STAGE) {
      setStages(message.content);
    }
  });

  // Auto-scroll log to bottom if user is near bottom
  useEffect(() => {
    const el = scrollAreaRef.current;
    if (!el) {
      return;
    }
    const distanceFromBottom = el.scrollHeight - el.scrollTop - el.clientHeight;
    const isNearBottom = distanceFromBottom < 80;
    if (isNearBottom) {
      el.scrollTo({ top: el.scrollHeight, behavior: 'smooth' });
    }
  }, [log]);

  return (
    <Paper className={styles.panelContainer} radius='md' withBorder>
      <PanelGroup direction='vertical' style={{ flex: 1 }}>
        <Panel minSize={0} collapsible={true}>
          {/* Additional content can be added here */}
        </Panel>
        <PanelResizeHandle className={styles.resizeHandle} />
        <Panel defaultSize={50} minSize={10} maxSize={90}>
          {/* Header */}
          <Flex justify='space-between' align='center' className={styles.header}>
            <Title order={3}>Log: {`${node?.data?.label ?? ''}`}</Title>
            <KanbanIconButton onClick={onClose} variant='subtle' size='sm' aria-label='Close log viewer'>
              <IconX size={18} />
            </KanbanIconButton>
          </Flex>

          {/* Body: Sidebar (stages) | Console */}
          <Flex className={styles.body}>
            {/* Left block: Stages */}
            <Box className={styles.stagesSection}>
              <ScrollArea className={styles.instancesScrollArea}>
                <Stack p='sm'>
                  {stages?.datas?.map((stage, idx) => (
                    <Paper
                      key={stage.name}
                      p='xs'
                      radius='md'
                      withBorder
                      data-status={stage.status}
                      className={`${styles.stagePaper} ${opened === idx ? styles.stagePaperActive : ''}`}
                      onClick={() => setOpened(opened === idx ? null : idx)}>
                      <Flex justify='space-between' align='center'>
                        <Group>
                          {statusIcon[stage.status]}
                          <Text size='sm'>{stage.name}</Text>
                          <Tooltip label={`Duration: ${formatTimeElapsed(stage.durationMillis ?? 0)}`}>
                            <Text size='xs' color='dimmed'>
                              {formatTimeElapsed(stage.durationMillis ?? 0)}
                            </Text>
                          </Tooltip>
                        </Group>
                        <IconChevronDown size={14} className={`${styles.chevron} ${opened === idx ? styles.chevronOpen : ''}`} />
                      </Flex>
                      <Text size='xs' className={styles.stageStatus} mt={4}>
                        {stage.status}
                      </Text>
                    </Paper>
                  ))}
                </Stack>
              </ScrollArea>
            </Box>

            {/* Right block: Console Log */}
            <Box className={styles.console}>
              {/* Console Header */}
              <Flex align='center' gap='xs' p='xs' className={styles.consoleHeader}>
                <IconTerminal2 size={16} className={styles.consoleHeaderTitle} />
                Console Log
              </Flex>

              {/* Scrollable Log Content */}
              <ScrollArea ref={scrollAreaRef} className={styles.scrollArea}>
                <Box p='xs' className={styles.logPre}>
                  {log || 'No logs available...'}
                </Box>
              </ScrollArea>
            </Box>
          </Flex>
        </Panel>
      </PanelGroup>
    </Paper>
  );
};

export default JenkinsLogViewer;
