import React from 'react';
import type { Node } from '@xyflow/react';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './JenkinsLogViewer';
import <PERSON><PERSON>LogViewer from './WlaLogViewer';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';

/**
 * LogViewer wrapper component.
 * Renders JenkinsLogViewer or WlaLogViewer based on node.data.application.
 *
 * @param node Node data for the workflow
 * @param onClose Callback to close the viewer
 * @returns JSX.Element
 */
type LogViewerProps = {
  node: Node;
  onClose: () => void;
};

export const LogViewer: React.FC<LogViewerProps> = ({ node, onClose }) => {
  if (node.data?.application === ChangeApplicationTypeEnum.JENKINS) {
    return <JenkinsLogViewer node={node} onClose={onClose} />;
  }
  if (node.data?.application === ChangeApplicationTypeEnum.WLA) {
    return <WlaLogViewer node={node} onClose={onClose} />;
  }
  return <></>;
};

export default LogViewer;
