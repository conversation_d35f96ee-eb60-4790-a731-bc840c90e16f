import React, { useMemo, useState } from 'react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { ChangeRequestRole, ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { KanbanAccordion, KanbanTabs, KanbanTabsType, KanbanText } from 'kanban-design-system';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import NodeCabGroupList from './NodeCabGroupList';
import { ChangeStageType } from '@common/constants/ChageStageType';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useSelector } from 'react-redux';

interface NodeCabWorkflowTabsProps {
  role: ChangeRequestRole;
  roleIdx: number;
  mode: EntityAction;
  modeApproval: ChangeRequestApprovalMode;
  refetchChangeRequestRoles: () => void;
  stage?: ChangeStageType;
  onRefetchAll?: () => void;
}

const NodeCabWorkflowTabs: React.FC<NodeCabWorkflowTabsProps> = ({
  mode,
  modeApproval,
  onRefetchAll,
  refetchChangeRequestRoles,
  role,
  roleIdx,
  stage,
}) => {
  const { control, getValues } = useFormContext<ChangeRequestRoleList>();
  const { fields: workflows } = useFieldArray({
    control,
    name: `roles.${roleIdx}.workflows`,
  });
  const currentUser = useSelector(getCurrentUser).userInfo;
  const [activeWorkFlowIdx, setActiveWorkFlowIdx] = useState<string>('0');
  const workflowTabs: KanbanTabsType = useMemo(() => {
    const tabs: KanbanTabsType = {};

    workflows.forEach((wl, workflowIdx) => {
      tabs[workflowIdx] = {
        content: (
          <NodeCabGroupList
            roleIdx={roleIdx}
            mode={mode}
            modeApproval={modeApproval}
            role={role}
            workflowIdx={workflowIdx}
            refetchChangeRequestRoles={refetchChangeRequestRoles}
            stage={stage}
            onRefetchAll={onRefetchAll}
          />
        ),
        title: wl.changeWorkflowName,
      };
    });

    return tabs;
  }, [mode, modeApproval, onRefetchAll, refetchChangeRequestRoles, role, roleIdx, stage, workflows]);

  const isChangeCoordinator = currentUser?.userName === getValues(`changeCoordinator`);
  const firstGroupAutoFocus = workflows.findIndex((wf) => wf.groups?.findIndex((g) => g.users?.some((u) => u.username === currentUser?.userName)));
  const activeFlowAccordion = isChangeCoordinator || firstGroupAutoFocus;

  const [openedPanels, setOpenedPanels] = useState<string[]>(() => (activeFlowAccordion ? [role.changeFlowNode?.name] : []));
  return (
    <Controller
      control={control}
      name={`activeRoles.${roleIdx}`}
      render={() => (
        <KanbanAccordion
          value={openedPanels}
          onChange={(value) => {
            if (!value) {
              return setOpenedPanels([]);
            }
            const newPanels = Array.isArray(value) ? value : [value];
            setOpenedPanels(newPanels);
          }}
          data={[
            {
              content: (
                <KanbanTabs
                  configs={{
                    value: activeWorkFlowIdx,
                    onChange: (value) => {
                      if (value) {
                        setActiveWorkFlowIdx(value);
                      }
                    },
                  }}
                  tabs={workflowTabs}
                />
              ),
              title: <KanbanText fw={500}>{`Level ${roleIdx + 1}: ${role.changeFlowNode?.name || ''}`}</KanbanText>,
              key: role.changeFlowNode?.name || '',
            },
          ]}
        />
      )}
    />
  );
};

export default NodeCabWorkflowTabs;
