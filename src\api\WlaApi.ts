import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createListSchema, createResponseSchema, ResponseData } from '@core/schema/Common';
import { RequestConfig } from '@core/api';
import { WlaTask, WlaTaskSchema } from '@core/schema/Wla';

export class WlaApi {
  static getTasks(changeNodeId: number, searchTxt?: string): RequestConfig<ResponseData<WlaTask[]>> {
    return createRequest({
      url: `${BaseURL.integrationWla}/tasks`,
      method: 'GET',
      params: { changeNodeId: changeNodeId, searchTxt: searchTxt },
      schema: createResponseSchema(createListSchema(WlaTaskSchema)),
    });
  }

  static getTasksDetals(changeNodeId: number, sysId: string): RequestConfig<ResponseData<WlaTask>> {
    return createRequest({
      url: `${BaseURL.integrationWla}/tasks`,
      method: 'GET',
      params: { changeNodeId: changeNodeId, sysId: sysId },
      schema: createResponseSchema(WlaTaskSchema),
    });
  }
}
