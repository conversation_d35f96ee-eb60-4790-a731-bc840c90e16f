import React, { useEffect, useState } from 'react';
import { Paper, Flex, Tabs } from '@mantine/core';
import { IconX, IconDeviceFloppy, IconSettings, IconVariable } from '@tabler/icons-react';
import { KanbanButton, KanbanContentLoading, KanbanIconButton, KanbanText } from 'kanban-design-system';
import { CustomNodeData } from '@pages/change/request/workflow/reactFlow/CustomNode';
import stylesCss from './NodeSettingsPanel.module.scss';
import type { Node } from '@xyflow/react';
import { useSearchParams } from 'react-router-dom';
import { EntityAction } from '@common/constants/EntityActionConstants';
import SettingsTab from './SettingsTab';
import ParametersTab from './ParametersTab';
import RunNodeManualButton from './RunNodeManualButton';
import { ChangeRequestWorkflowApi } from '@api/ChangeRequestWorkflowApi';
import useFetch from '@core/hooks/useFetch';

type NodeSettingsPanelProps = {
  changeId: number;
  workflowId: number;
  node: Node;
  onClose: () => void;
  onSave?: (nodeId: string, data?: CustomNodeData) => void;
};

export const NodeSettingsPanel: React.FC<NodeSettingsPanelProps> = ({ changeId, node, onClose, onSave, workflowId }) => {
  const [activeTab, setActiveTab] = useState<string | null>('parameters');
  const [customNodeData, setCustomNodeData] = useState<CustomNodeData | undefined>(undefined);
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const isViewMode = actionParam === EntityAction.VIEW || !onSave;

  const { data: executionLastest, isFetching } = useFetch(ChangeRequestWorkflowApi.findExecutionLastest(changeId, workflowId), {
    showLoading: false,
  });
  useEffect(() => {
    try {
      const nodeData: CustomNodeData = node?.data as CustomNodeData;
      setCustomNodeData(nodeData);
    } catch (error) {
      /* empty */
    }
  }, [node]);

  const handleSave = () => {
    if (onSave) {
      onSave(`${node.id}`, customNodeData);
      onClose();
    }
  };

  return (
    <Paper className={stylesCss.settingsPanel}>
      {/* We should internal loading instead of global loading */}
      <KanbanContentLoading
        loading={isFetching}
        wrapperProps={{
          className: stylesCss.contentPanel,
        }}>
        <Flex justify='space-between' align='center' className={stylesCss.header}>
          <KanbanText size='xl' fw={700}>
            Configure: {customNodeData?.label}
          </KanbanText>
          <KanbanIconButton onClick={onClose} variant='subtle' size='sm'>
            <IconX size={18} />
          </KanbanIconButton>
        </Flex>

        <Tabs value={activeTab} onChange={setActiveTab} className={stylesCss.tabs}>
          <Tabs.List grow>
            <Tabs.Tab value='parameters' leftSection={<IconVariable size={16} />}>
              Parameters
            </Tabs.Tab>
            <Tabs.Tab value='settings' leftSection={<IconSettings size={16} />}>
              Settings
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='parameters' pt='md' className={stylesCss.tabPanel}>
            <ParametersTab customNodeData={customNodeData} setCustomNodeData={setCustomNodeData} />
          </Tabs.Panel>

          <Tabs.Panel value='settings' pt='md' className={stylesCss.tabPanel}>
            <SettingsTab customNodeData={customNodeData} setCustomNodeData={setCustomNodeData} />
          </Tabs.Panel>
        </Tabs>

        {executionLastest?.data?.id && <RunNodeManualButton changeId={changeId} workflowId={workflowId} nodeId={node.id} />}

        {!executionLastest?.data?.id && (
          <Flex gap='sm' className={stylesCss.footer}>
            <KanbanButton variant='outline' onClick={onClose} className={stylesCss.cancelButton}>
              Cancel
            </KanbanButton>
            <KanbanButton disabled={isViewMode} onClick={handleSave} leftSection={<IconDeviceFloppy size={16} />} className={stylesCss.saveButton}>
              Save
            </KanbanButton>
          </Flex>
        )}
      </KanbanContentLoading>
    </Paper>
  );
};

export default NodeSettingsPanel;
