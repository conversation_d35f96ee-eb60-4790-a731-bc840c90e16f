/* Comment by: K-tool gencode */
/* Copied from WorkflowDetailPage.module.scss */
.pageContainer {
  height: 100%;
}

.workflowCard {
  height: 95%;
  position: relative;
  overflow: hidden;
}

.cardHeader {
  padding: 8px 16px;
  margin-bottom: 10px;
}

.flowWrapper {
  height: 95%;
  width: 100%;
  flex: 1;
  min-height: 500px;
}

/* Inline style conversions */
.floatingSidebarButton {
  position: absolute;
  left: 0px;
  top: 46px;
}

/* Improved sidebarPaper style */
.sidebarPaper {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 350px;
  z-index: 100;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  height: 100%;
  padding: 0;
  background: #f7faff;
  /* subtle blue background */
  border-radius: 16px 0 0 16px;
  box-shadow: 2px 0 16px 0 rgba(44, 62, 80, 0.10);
  border-right: 1.5px solid #e0e0e0;
  overflow: hidden;
  transition: box-shadow 0.2s;
}

/* Sidebar header and close button remain unchanged */
.sidebarHeader {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebarCloseButton {
  width: 28px;
  height: 28px;
  min-width: 28px;
  padding: 0;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Add more padding to scroll area for better spacing */
.sidebarScrollArea {
  padding: 10px;
  box-sizing: border-box;
}

/* Node summary and executions */
.summaryPaper {
  background: linear-gradient(135deg, #f7fafc 60%, #e3e9f3 100%);
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
  margin-bottom: 5px;
  padding: 5px;
  border-radius: 10px;
}

.statusStarted {
  color: #6c7a89;
}

.statusValue {
  font-weight: 600;
}

.statusLabel {
  font-weight: 700;
}

.statusSuccess {
  color: #2ecc71;
}

.statusFailure {
  color: #e74c3c;
}

.statusRunning {
  color: #f39c12;
}

.statusOther {
  color: #888;
}

.nodeExecutionsPaper {
  background: #fff;
  border: 1px solid #e0e0e0;
  padding: 12px;
  border-radius: 10px;
  margin-bottom: 12px;
}

.nodeExecutionsTitle {
  color: #2d3a4a;
  margin-bottom: 8px;
}

.nodeExecutionsList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nodeExecutionItem {
  margin-bottom: 14px;
  padding: 12px;
  border-radius: 8px;
  background: #f9f9fb;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nodeExecutionName {
  font-weight: 600;
  color: #3b4a5a;
  font-size: 15px;
}

.nodeExecutionTime {
  font-size: 13px;
  color: #6c7a89;
}

.nodeExecutionDuration {
  font-size: 13px;
  color: #6c7a89;
}

.nodeExecutionStatus {
  font-size: 13px;
}

.nodeExecutionStatusValue {
  font-weight: 600;
}

.nodeExecutionStatusSuccess {
  color: #2ecc71;
}

.nodeExecutionStatusFailure {
  color: #e74c3c;
}

.nodeExecutionStatusRunning {
  color: #f39c12;
}

.nodeExecutionStatusOther {
  color: #888;
}