import { ChangeRequestDocumentEnums, ChangeRequestDocumentOwnerForm } from '@models/ChangeRequestDocumentGroupModel';
import { useDocumentContext } from '@context/DocumentPermissionContext';
import { useGetCurrentUser } from './useGetCurrentUser';

interface PermissionOptions {
  owners?: ChangeRequestDocumentOwnerForm[];
  currentOwner?: ChangeRequestDocumentOwnerForm;
}

export function useChangeRequestDocumentPermission({ currentOwner, owners }: PermissionOptions = {}) {
  const { hasRole } = useDocumentContext();
  const currentUser = useGetCurrentUser()?.userInfo?.userName ?? '';

  const isCoordinator = hasRole(ChangeRequestDocumentEnums.Role.Enum.COORDINATOR);
  const isOwner = hasRole(ChangeRequestDocumentEnums.Role.Enum.OWNER);
  const isApprover = hasRole(ChangeRequestDocumentEnums.Role.Enum.DOCUMENT_APPROVER);
  const isGroupApprover = hasRole(ChangeRequestDocumentEnums.Role.Enum.GROUP_APPROVER);

  const canEditCab =
    isOwner && !!owners?.some((o) => o?.status === ChangeRequestDocumentEnums.ActionStatus.Enum.SENT_TO_OWNER && o?.username === currentUser);

  const canEditRow =
    isOwner && ChangeRequestDocumentEnums.ActionStatus.Enum.SENT_TO_OWNER === currentOwner?.status && currentOwner?.username === currentUser;

  return {
    currentUser,
    isCoordinator,
    isOwner,
    isApprover,
    isGroupApprover,
    canEditCab,
    canEditRow,
  };
}
