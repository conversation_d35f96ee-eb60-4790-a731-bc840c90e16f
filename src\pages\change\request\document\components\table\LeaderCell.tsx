import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Flex, ActionIcon } from '@mantine/core';
import { IconCopy } from '@tabler/icons-react';
import UserSelectComponent from '../selectBox/UserSelectComponent';
import { getApproversByLevel, parseClipboardApprovers, updateApproversByLevel } from '../../utils/TableUtils';
import { ChangeRequestDocumentGroupFormWrapper } from '@models/ChangeRequestDocumentGroupModel';
import { UserSelectedModel } from '@models/UserModel';

type Props = {
  name: `items.${number}.owners.${number}.documents.${number}.approvers`;
  level: number;
  canEditRow: boolean;
  onCopy: (users: { username: string; displayName?: string | null }[]) => void;
};

export const LeaderCell = ({ canEditRow, level, name, onCopy }: Props) => {
  const { control } = useFormContext<ChangeRequestDocumentGroupFormWrapper>();

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={undefined}
      render={({ field, fieldState }) => {
        const value = field.value || [];
        const leaderList = getApproversByLevel(value, level);

        const handleChange = (val: UserSelectedModel[]) => {
          const mapped = val.map((v) => ({ username: v.id, displayName: v.name }));
          field.onChange(updateApproversByLevel(value, level, mapped));
        };

        const handlePaste = (e: React.ClipboardEvent<HTMLDivElement>) => {
          e.preventDefault();
          const parsed = parseClipboardApprovers(e, level);
          if (parsed) {
            field.onChange(updateApproversByLevel(value, level, parsed));
          }
        };

        return (
          <Flex gap={4} align='center' onPaste={handlePaste}>
            <UserSelectComponent
              value={leaderList.map((leader) => ({ id: leader.username, name: leader.displayName || '' }))}
              onChange={handleChange}
              error={fieldState.error?.message}
              disable={!canEditRow}
              onBlur={field.onBlur}
            />
            <ActionIcon variant='subtle' size='sm' color='black' disabled={!canEditRow} onClick={() => onCopy?.(leaderList)} title='Copy'>
              <IconCopy />
            </ActionIcon>
          </Flex>
        );
      }}
    />
  );
};
