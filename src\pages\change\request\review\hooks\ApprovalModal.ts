import { useState, useCallback } from 'react';
import {
  ChangeRequestReviewApprovalStatusEnum,
  ChangeRequestReviewApprovalStatusType,
} from '@common/constants/ChangeRequestReviewApprovalStatusConstants';
import { ApprovalModalState } from '../types/ReviewTypes';

export const useApprovalModal = () => {
  const [modalState, setModalState] = useState<ApprovalModalState>({
    isOpen: false,
    action: null,
    comment: '',
  });

  const openModal = useCallback((action: ChangeRequestReviewApprovalStatusType) => {
    setModalState({
      isOpen: true,
      action,
      comment: '',
    });
  }, []);

  const closeModal = useCallback(() => {
    setModalState({
      isOpen: false,
      action: null,
      comment: '',
    });
  }, []);

  const updateComment = useCallback((comment: string) => {
    setModalState((prev) => ({
      ...prev,
      comment,
    }));
  }, []);

  const handleAccept = useCallback(() => {
    openModal(ChangeRequestReviewApprovalStatusEnum.Enum.ACCEPT);
  }, [openModal]);

  const handleReject = useCallback(() => {
    openModal(ChangeRequestReviewApprovalStatusEnum.Enum.REJECT);
  }, [openModal]);

  return {
    modalState,
    openModal,
    closeModal,
    updateComment,
    handleAccept,
    handleReject,
  };
};
