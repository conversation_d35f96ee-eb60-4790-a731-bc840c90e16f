import React, { useCallback, useEffect, useState } from 'react';
import { Control, Controller, Noop, useFormContext, useWatch } from 'react-hook-form';
import { Box, Group, Tooltip } from '@mantine/core';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import useFetch from '@core/hooks/useFetch';
import { getDefaultTableAffected, KanbanSelect, KanbanText, SortOrder } from 'kanban-design-system';
import { ChangeFlowNodeApi } from '@api/ChangeFlowNodeApi';
import { ChangeFlowNodeTypeEnum } from '@models/ChangeFlowModel';
import { EntityAction } from '@common/constants/EntityActionConstants';
import styled from '../ChangeRequestDetail.module.scss';
import { COMMON_MAX_DROP_DOWN_HEIGHT, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ChangeRoleUserReponse } from '@core/schema/User';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useSelector } from 'react-redux';
import { ChangeRequestRoleUser } from '@core/schema/ChangeRequestRoleUser';
import { WAITING_APPROVAL } from './approval/ChangeRequestApprovalComponent';
import { ChangeRequestApprovalStatusEnum } from '@core/schema/ChangeRequestApproval';

interface ItemProps {
  control: Control<ChangeRequestRoleList>;
  roleIdx: number;
  cabGroupIdx: number;
  cabGroupUserIdx: number;
  changeFlowNodeId: number;
  mode: EntityAction;
  workflowIdx: number;
  modeApproval: ChangeRequestApprovalMode;
}

type UserSelectItem = {
  value: string;
  label: string;
  isActive: boolean;
};

const ApprovalUserSelect: React.FC<ItemProps> = ({
  cabGroupIdx,
  cabGroupUserIdx,
  changeFlowNodeId,
  control,
  mode,
  modeApproval,
  roleIdx,
  workflowIdx,
}) => {
  const {
    clearErrors,
    formState: { errors },
    getValues,
    setError,
  } = useFormContext<ChangeRequestRoleList>();
  const [searchTerm, setSearchTerm] = useState('');
  const [modifiedSearch, setModifiedSearch] = useState(true);
  const [userOptions, setUserOptions] = useState<UserSelectItem[]>([]);
  const [shouldFetchUser, setShouldFetchUser] = useState(true);

  const userPath = `roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}.users.${cabGroupUserIdx}` as const;
  const changeRequestRoleUser = getValues(`${userPath}`) as ChangeRequestRoleUser;
  const changeRequestApproval = changeRequestRoleUser.changeRequestApproval || WAITING_APPROVAL;
  const changeWorkflowId = getValues(`roles.${roleIdx}.workflows.${workflowIdx}.changeWorkflowId`);
  const userNameFormPath = `${userPath}.username` as const;

  const roleName = getValues(`roles.${roleIdx}.changeFlowNode.name`);
  const roleType = useWatch({ control, name: `roles.${roleIdx}.changeFlowNode.type` });
  const currentUser = useSelector(getCurrentUser).userInfo;

  const { data: usersResponse } = useFetch(
    ChangeFlowNodeApi.findAllApprovalUsersById(changeFlowNodeId, {
      ...getDefaultTableAffected(),
      advancedFilterMapping: {
        ['name']: {
          filterOption: 'contains',
          value: {
            fromValue: searchTerm,
          },
        },
        ['username']: {
          filterOption: 'contains',
          value: {
            fromValue: searchTerm,
          },
        },
        ['email']: {
          filterOption: 'contains',
          value: {
            fromValue: searchTerm,
          },
        },
      },
      sortedBy: 'name',
      sortOrder: SortOrder.ASC,
      rowsPerPage: 200,
    }),
    {
      enabled: shouldFetchUser && modifiedSearch && !!changeFlowNodeId,
      showLoading: false,
    },
  );

  useEffect(() => {
    if (!usersResponse?.data?.content) {
      return;
    }

    const apiUsers: UserSelectItem[] = usersResponse.data.content.map((roleUser: ChangeRoleUserReponse) => ({
      value: roleUser.userName,
      label: `${roleUser.name} (${roleUser.userName})`,
      isActive: roleUser.isActive,
    }));

    setUserOptions((prevOptions) => {
      const currentValue = getValues(userNameFormPath);
      let selected = prevOptions.find((u) => u.value === currentValue);
      if (!selected && currentValue) {
        selected = { value: currentValue, label: `${currentValue} (${currentValue})`, isActive: false };
      }

      if (selected && !apiUsers.some((u) => u.value === selected.value)) {
        return [selected, ...apiUsers];
      }

      return apiUsers;
    });

    setShouldFetchUser(false);
    setModifiedSearch(false);
  }, [usersResponse?.data?.content, getValues, userNameFormPath]);

  const handleValidate = useCallback(
    (_?: Noop) => {
      if (!getValues(userNameFormPath)) {
        setError(userNameFormPath, { message: 'Please fill in all the required fields!' });
      } else {
        clearErrors(userNameFormPath);
      }
    },
    [clearErrors, getValues, setError, userNameFormPath],
  );

  const fieldError = errors?.roles?.[roleIdx]?.workflows?.[workflowIdx]?.groups?.[cabGroupIdx]?.users?.[cabGroupUserIdx]?.username;

  const isViewMode = EntityAction.VIEW === mode;
  const isChangeCoordinator = currentUser?.userName === getValues(`changeCoordinator`);
  const canEdit =
    !isViewMode &&
    isChangeCoordinator &&
    (changeWorkflowId === 0 || changeRequestRoleUser.changeNodeId === null) &&
    changeRequestApproval.overAllStatus !== ChangeRequestApprovalStatusEnum.Enum.ACCEPT &&
    changeRequestApproval.overAllStatus !== ChangeRequestApprovalStatusEnum.Enum.REJECT;

  return (
    <Box bg={ChangeFlowNodeTypeEnum.Enum.CAB === roleType ? 'white' : ''}>
      <Group justify='flex-start' align='center' gap={8} mb={8}>
        <Tooltip disabled={ChangeFlowNodeTypeEnum.Enum.CAB === roleType} label={roleName} multiline maw='30%' position='top-start' openDelay={500}>
          <KanbanText className={styled.roleName} w={ChangeFlowNodeTypeEnum.Enum.CAB === roleType ? '' : '300'}>
            {ChangeFlowNodeTypeEnum.Enum.CAB === roleType || ChangeRequestApprovalMode.APPROVAL === modeApproval ? '' : roleName}
          </KanbanText>
        </Tooltip>

        <Controller
          control={control}
          name={userNameFormPath}
          render={({ field }) => {
            const selectedValue = userOptions.find((u) => u.value === field.value) ? field.value : '';
            return (
              <KanbanSelect
                {...field}
                defaultValue={getValues(userNameFormPath) || ''}
                autoChangeValueByOptions={false}
                value={selectedValue}
                data={userOptions.map((u) => ({
                  value: u.value,
                  label: u.label,
                }))}
                mb={0}
                disabled={!canEdit}
                searchable
                allowDeselect={false}
                clearable={canEdit}
                onSearchChange={(val) => {
                  setSearchTerm(val);
                  const matched = userOptions.find((u) => u.label === val || u.value === val);
                  if (matched) {
                    setSearchTerm('');
                  }
                  setModifiedSearch(true);
                  setShouldFetchUser(true);
                }}
                onChange={(val) => {
                  if (val !== field.value) {
                    field.onChange(val);
                  }
                }}
                maxLength={COMMON_MAX_LENGTH}
                maxDropdownHeight={COMMON_MAX_DROP_DOWN_HEIGHT}
                styles={{ root: { flexGrow: 1 } }}
                onBlur={() => canEdit && handleValidate()}
                error={fieldError?.message}
              />
            );
          }}
        />
      </Group>
    </Box>
  );
};

export default ApprovalUserSelect;
