import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import { Table, Group, Anchor, Flex, ActionIcon } from '@mantine/core';
import { ChangeRequestDocumentGroupApi } from '@api/ChangeRequestDocumentGroupApi';
import useMutate from '@core/hooks/useMutate';
import {
  ChangeRequestDocumentActionStatusType,
  ChangeRequestDocumentEnums,
  ChangeRequestDocumentGroupModel,
  ChangeRequestDocumentModel,
  ChangeRequestSendToApproverRequest,
  ChangeRequestSendToOwnerRequest,
  LimitedActionStatus,
} from '@models/ChangeRequestDocumentGroupModel';
import classes from './table/CommonTable.module.scss';
import { saveAs } from 'file-saver';
import { KanbanCheckbox, KanbanText } from 'kanban-design-system';
import { IconFile, IconLink, IconSquareCheckFilled, IconSquareX } from '@tabler/icons-react';
import { renderStatusPill, renderUserApprovePill, renderUserPill } from '@pages/change/request/document/utils/TableUtils';
import { DocumentApproverLevelEnum, DocumentApproverLevels } from '@common/constants/ChangeDocumentConstants';
import { ChangeRequestDocumentSelection, ChangeRequestOwnerSelection } from '@pages/change/request/document/DocumentGroupViewPage';
import { useGetCurrentUser } from '@core/hooks/useGetCurrentUser';
import { useChangeRequestDocumentPermission } from '@core/hooks/useChangeRequestDocumentPermission';
import { normalizeUrl } from '@common/utils/UrlUtils';
import { getCheckAllState, handleCheckAll } from '@common/utils/CheckBoxUtils';

export const NON_BREAKING_SPACE = '\u00A0';

type DocumentViewTableProps = {
  changeRequestId: number;
  documentGroup: ChangeRequestDocumentGroupModel;
  selectedOwners: ChangeRequestSendToOwnerRequest[];
  selectedDocuments: ChangeRequestSendToApproverRequest[];
  onToggleOwner: (ownerSelection: ChangeRequestOwnerSelection) => void;
  onToggleDocument: (documentSelection: ChangeRequestDocumentSelection) => void;
  handleActionApproveOrReject: (
    action: ChangeRequestDocumentActionStatusType,
    isActionAll: boolean,
    docId?: number,
    documentApproverLevel?: number,
  ) => void;
};

export function AccordionPanelView({
  changeRequestId,
  documentGroup,
  handleActionApproveOrReject,
  onToggleDocument,
  onToggleOwner,
  selectedDocuments,
  selectedOwners,
}: DocumentViewTableProps) {
  const userName = useGetCurrentUser()?.userInfo?.userName ?? '';
  const { isApprover, isCoordinator, isGroupApprover, isOwner } = useChangeRequestDocumentPermission();
  const totalDocumentsInGroup = useMemo(
    () => documentGroup.owners.reduce((sum, o) => sum + (o.documents?.length || 1), 0) || 1,
    [documentGroup.owners],
  );
  const totalDocumentsInGroupForCheckbox = documentGroup.owners.reduce((sum, o) => sum + (o.documents?.length || 0), 0) || 0;

  const { mutate: downloadMutate } = useMutate<Blob, { changeRequestId: number; documentGroupId: number; documentName: string }>(
    ({ changeRequestId, documentGroupId }) => ChangeRequestDocumentGroupApi.downloadFile(changeRequestId, documentGroupId),
    {
      successNotification: 'Document downloaded successfully',
      errorNotification: (error) => ({ message: error.message }),
      onSuccess: (res, variables) => {
        if (res instanceof Blob) {
          const { documentName } = variables;
          saveAs(res, documentName);
        }
      },
    },
  );

  const handleDownloadFile = useCallback(
    (documentGroupId: number, documentName: string) => {
      downloadMutate({ changeRequestId, documentGroupId, documentName });
    },
    [changeRequestId, downloadMutate],
  );

  const canUserApproveLevel = (doc: ChangeRequestDocumentModel | undefined, userName: string, level: number) => {
    if (!doc) {
      return false;
    }

    return (
      doc.status === ChangeRequestDocumentEnums.ActionStatus.Enum.SENT_TO_LEADER &&
      doc.approvers.some((a) => a.user.username === userName && a.documentApproverLevel === level && LimitedActionStatus.includes(a.status))
    );
  };

  const canShowByLevel = useMemo(() => {
    let canLeader1 = false;
    let canLeader2 = false;

    for (const owner of documentGroup.owners ?? []) {
      for (const doc of owner.documents ?? []) {
        if (canUserApproveLevel(doc, userName, DocumentApproverLevelEnum.LEVEL_1)) {
          canLeader1 = true;
        }
        if (canUserApproveLevel(doc, userName, DocumentApproverLevelEnum.LEVEL_2)) {
          canLeader2 = true;
        }
        if (canLeader1 && canLeader2) {
          return {
            [DocumentApproverLevelEnum.LEVEL_1]: true,
            [DocumentApproverLevelEnum.LEVEL_2]: true,
          };
        }
      }
    }

    return {
      [DocumentApproverLevelEnum.LEVEL_1]: canLeader1,
      [DocumentApproverLevelEnum.LEVEL_2]: canLeader2,
    };
  }, [documentGroup, userName]);

  const canShowLeader1Actions = canShowByLevel[DocumentApproverLevelEnum.LEVEL_1];
  const canShowLeader2Actions = canShowByLevel[DocumentApproverLevelEnum.LEVEL_2];

  const renderDocumentCell = (document?: ChangeRequestDocumentModel) => {
    if (!document) {
      return NON_BREAKING_SPACE;
    }

    if (document.type === ChangeRequestDocumentEnums.DocumentType.Enum.FILE) {
      return (
        <Group gap={4}>
          <IconFile size={14} />
          <Anchor size='xs' component='button' onClick={() => handleDownloadFile(document.id, document.documentName)}>
            {document.documentName || 'Document File'}
          </Anchor>
        </Group>
      );
    }

    if (document.type === ChangeRequestDocumentEnums.DocumentType.Enum.URL && document.documentUrl) {
      return (
        <Group gap={4}>
          <IconLink size={14} />
          <Anchor size='xs' href={normalizeUrl(document.documentUrl)} target='_blank' rel='noopener noreferrer'>
            {document.documentName || 'Document Url'}
          </Anchor>
        </Group>
      );
    }

    return document.documentName || NON_BREAKING_SPACE;
  };

  const renderApproveRejectButtons = (docId: number | undefined, level: number) => (
    <Flex align='center'>
      {[
        { action: ChangeRequestDocumentEnums.ActionStatus.Enum.APPROVED, icon: <IconSquareCheckFilled size='1.8rem' />, title: 'Approval' },
        { action: ChangeRequestDocumentEnums.ActionStatus.Enum.REJECTED, icon: <IconSquareX size='1.8rem' />, title: 'Reject' },
      ].map(({ action, icon, title }) => (
        <ActionIcon key={action} size='lg' title={title} variant='white' onClick={() => handleAction(action, docId, level)}>
          {icon}
        </ActionIcon>
      ))}
    </Flex>
  );

  const handleAction = (action: ChangeRequestDocumentActionStatusType, docId?: number, documentApproverLevel?: number) => {
    handleActionApproveOrReject(action, false, docId, documentApproverLevel);
  };

  const handleToggleOwner = useCallback(
    (username: string, checked: boolean) => {
      onToggleOwner({ referenceId: documentGroup.id, username, checked });
    },
    [documentGroup.id, onToggleOwner],
  );

  return (
    <>
      <Table striped highlightOnHover withTableBorder withColumnBorders className={classes.table}>
        <Table.Thead>
          <Table.Tr>
            {isCoordinator && (
              <Table.Th className={classes.headerCellCheckBox}>
                <KanbanCheckbox
                  {...getCheckAllState({
                    items: documentGroup.owners,
                    isSelected: (owner) =>
                      selectedOwners.some((sel) => sel.referenceId === documentGroup.id && sel.usernames.includes(owner.username)),
                  })}
                  onChange={(e) =>
                    handleCheckAll(
                      {
                        items: documentGroup.owners,
                        isSelected: () => false,
                        onToggle: (owner, checked) => onToggleOwner({ referenceId: documentGroup.id, username: owner.username, checked }),
                      },
                      e.target.checked,
                    )
                  }
                />
              </Table.Th>
            )}
            <Table.Th className={classes.headerCell}>Owner</Table.Th>
            {(isOwner || isApprover || isGroupApprover) && totalDocumentsInGroupForCheckbox > 0 && (
              <Table.Th className={classes.headerCellCheckBox}>
                <KanbanCheckbox
                  {...getCheckAllState({
                    items: documentGroup.owners.flatMap((o) => o.documents ?? []),
                    isSelected: (doc) => selectedDocuments.some((sel) => sel.groupId === documentGroup.id && sel.docIds.includes(doc.id)),
                  })}
                  onChange={(e) =>
                    handleCheckAll(
                      {
                        items: documentGroup.owners.flatMap((o) => o.documents ?? []),
                        isSelected: () => false,
                        onToggle: (doc, checked) =>
                          onToggleDocument({
                            groupId: documentGroup.id,
                            docId: doc.id,
                            checked,
                            documentApproverLevel: doc.approvers.find((a) => a.user.username === userName && LimitedActionStatus.includes(a.status))
                              ?.documentApproverLevel,
                            documentStatus: doc.status,
                          }),
                      },
                      e.target.checked,
                    )
                  }
                />
              </Table.Th>
            )}
            <Table.Th className={classes.headerCell}>Document</Table.Th>
            <Table.Th className={classes.headerCell}>Leader level 1</Table.Th>
            {canShowLeader1Actions && <Table.Th className={classes.headerCellCheckBox}></Table.Th>}
            <Table.Th className={classes.headerCell}>Leader level 2</Table.Th>
            {canShowLeader2Actions && <Table.Th className={classes.headerCellCheckBox}></Table.Th>}
            <Table.Th className={classes.headerCell}>CAB</Table.Th>
            <Table.Th className={classes.headerCell}>Status</Table.Th>
            <Table.Th className={classes.headerCell}>Comment</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {documentGroup.owners.flatMap((owner, ownerIndex) => {
            const docs = owner.documents && owner.documents.length > 0 ? owner.documents : [undefined];
            const rowSpan = docs.length;

            return docs.map((doc, docIndex) => (
              <Table.Tr key={`${owner.username}-${docIndex}`}>
                {/* Checkbox + Owner */}
                {docIndex === 0 && (
                  <>
                    {isCoordinator && (
                      <Table.Td className={classes.cell} rowSpan={rowSpan} style={{ verticalAlign: 'top' }}>
                        <KanbanCheckbox
                          onChange={(e) => handleToggleOwner(owner.username, e.target.checked)}
                          checked={selectedOwners.some((item) => item.referenceId === documentGroup.id && item.usernames.includes(owner.username))}
                        />
                      </Table.Td>
                    )}
                    <Table.Td className={classes.cell} rowSpan={rowSpan} style={{ verticalAlign: 'top' }}>
                      {renderUserPill(owner)}
                    </Table.Td>
                  </>
                )}

                {/* Checkbox send for leader */}
                {(isOwner || isApprover || isGroupApprover) && totalDocumentsInGroupForCheckbox > 0 && (
                  <Table.Td className={classes.cell} style={{ verticalAlign: 'top' }}>
                    {doc ? (
                      <KanbanCheckbox
                        onChange={(e) =>
                          onToggleDocument({
                            groupId: documentGroup.id,
                            docId: doc.id,
                            checked: e.target.checked,
                            documentApproverLevel: doc.approvers.find((a) => a.user.username === userName)?.documentApproverLevel,
                            documentStatus: doc.status,
                          })
                        }
                        checked={selectedDocuments.some((item) => item.groupId === documentGroup.id && item.docIds.includes(doc.id))}
                      />
                    ) : (
                      NON_BREAKING_SPACE
                    )}
                  </Table.Td>
                )}

                {/* Document cell */}
                <Table.Td className={classes.cell}>{renderDocumentCell(doc)}</Table.Td>

                {/* Approvers by level */}
                {DocumentApproverLevels.map((level) => (
                  <React.Fragment key={`doc-${doc?.id ?? docIndex}-level-${level}`}>
                    <Table.Td className={classes.cell}>
                      {doc
                        ? doc.approvers.filter((a) => a.documentApproverLevel === level).map((approver) => renderUserApprovePill(approver))
                        : NON_BREAKING_SPACE}
                    </Table.Td>
                    {canShowByLevel[level] && (
                      <Table.Td className={classes.cell}>
                        {isApprover && canUserApproveLevel(doc, userName, level) ? renderApproveRejectButtons(doc?.id, level) : NON_BREAKING_SPACE}
                      </Table.Td>
                    )}
                  </React.Fragment>
                ))}

                {/* CAB */}
                {ownerIndex === 0 && docIndex === 0 && (
                  <Table.Td className={classes.cell} rowSpan={totalDocumentsInGroup} style={{ verticalAlign: 'top' }}>
                    {(documentGroup.approvers ?? []).map((approver) => renderUserPill(approver.user))}
                  </Table.Td>
                )}

                {/* Status */}
                <Table.Td className={classes.cell}>
                  {doc?.status ? renderStatusPill(doc.status) : owner.status && renderStatusPill(owner.status)}
                </Table.Td>

                {/* Comment */}
                <Table.Td className={classes.cell}>
                  {doc?.comments
                    ? doc.comments.map((cmt, index) => (
                        <KanbanText key={index} size='sm'>
                          <KanbanText fw={500}>{cmt?.createdBy?.displayName}</KanbanText> - {cmt.content}
                        </KanbanText>
                      ))
                    : NON_BREAKING_SPACE}
                </Table.Td>
              </Table.Tr>
            ));
          })}
        </Table.Tbody>
      </Table>
    </>
  );
}
