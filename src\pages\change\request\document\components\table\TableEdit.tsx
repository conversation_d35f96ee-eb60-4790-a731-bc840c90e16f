import { ActionIcon, Box, Flex, Table } from '@mantine/core';
import classes from './CommonTable.module.scss';
import { KanbanButton, KanbanText } from 'kanban-design-system';
import { IconCircleMinus, IconCirclePlus, IconPlus } from '@tabler/icons-react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ChangeRequestDocumentGroupFormWrapper } from '@models/ChangeRequestDocumentGroupModel';
import { OwnerRow } from '@pages/change/request/document/components/table/OwnerRow';
import { useChangeRequestDocumentPermission } from '@core/hooks/useChangeRequestDocumentPermission';
import { DocumentApproverLevelEnum } from '@common/constants/ChangeDocumentConstants';

interface TableEditProps {
  itemName: `items.${number}`;
}

const TableEdit = React.memo(function TableEdit({ itemName }: TableEditProps) {
  const { control, getValues, setValue } = useFormContext<ChangeRequestDocumentGroupFormWrapper>();
  const [openedPopoverId, setOpenedPopoverId] = useState<string | null>(null);

  const {
    append,
    fields,
    remove: removeOwner,
  } = useFieldArray({
    control,
    name: `${itemName}.owners`,
  });

  const watchedOwners = useWatch({
    control,
    name: `${itemName}.owners`,
  });

  const { canEditCab, isCoordinator } = useChangeRequestDocumentPermission({
    owners: watchedOwners,
  });

  const hasApproverLevel2 = watchedOwners?.some((owner) =>
    owner?.documents?.some((doc) => doc?.approvers?.some((a) => a.documentApproverLevel === DocumentApproverLevelEnum.LEVEL_2)),
  );

  const [isLeaderLevel2Visible, setLeaderLevel2Visible] = useState(hasApproverLevel2 || false);

  useEffect(() => {
    if (hasApproverLevel2) {
      setLeaderLevel2Visible(true);
    }
  }, [hasApproverLevel2]);

  const totalDocumentRows = useMemo(
    () => watchedOwners?.reduce((sum, owner) => sum + Math.max(owner?.documents?.length ?? 0, 1), 0) ?? 0,
    [watchedOwners],
  );

  const handleAddOwner = useCallback(() => append({ username: '' }), [append]);

  const handleRemoveLeaderColumn = useCallback(() => {
    const currentOwners = getValues(`${itemName}.owners`) ?? [];

    const cleanedOwners = currentOwners.map((owner) => ({
      ...owner,
      documents:
        owner.documents?.map((doc) => ({
          ...doc,
          approvers: doc.approvers?.filter((a) => a.documentApproverLevel !== DocumentApproverLevelEnum.LEVEL_2) ?? [],
        })) ?? [],
    }));

    setValue(`${itemName}.owners`, cleanedOwners, { shouldDirty: true, shouldValidate: true });
    setLeaderLevel2Visible(false);
  }, [getValues, setValue, itemName]);

  return (
    <Flex gap='md' direction='column' mb={16}>
      <Table withColumnBorders striped highlightOnHover verticalSpacing='sm' horizontalSpacing='md' className={classes.table}>
        <Table.Thead>
          <Table.Tr>
            <Table.Th className={classes.headerCell}>Owner</Table.Th>
            <Table.Th className={classes.headerCell}>Document</Table.Th>
            <Table.Th className={classes.headerCell}>
              <Flex justify='space-between' align='center'>
                <KanbanText size='sm' fw={500}>
                  {isLeaderLevel2Visible ? 'Leader level 1' : 'Leader'}
                </KanbanText>
                {!isLeaderLevel2Visible && (
                  <ActionIcon title='Add Leader Column' variant='subtle' onClick={() => setLeaderLevel2Visible(true)}>
                    <IconCirclePlus size={18} color='black' />
                  </ActionIcon>
                )}
              </Flex>
            </Table.Th>
            {isLeaderLevel2Visible && (
              <Table.Th className={classes.headerCell}>
                <Flex justify='space-between' align='center'>
                  <KanbanText size='sm' fw={500}>
                    Leader level 2
                  </KanbanText>
                  <ActionIcon title='Remove Leader Column' variant='subtle' color='black' onClick={handleRemoveLeaderColumn}>
                    <IconCircleMinus size={18} />
                  </ActionIcon>
                </Flex>
              </Table.Th>
            )}
            <Table.Th className={classes.headerCell}>CAB</Table.Th>
            <Table.Th className={classes.headerCell}>Status</Table.Th>
            <Table.Th className={classes.headerCell}>Comment</Table.Th>
            <Table.Th className={classes.headerCell}></Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {fields.map((owner, ownerIndex) => (
            <OwnerRow
              key={owner.id}
              isLeaderLevel2Visible={isLeaderLevel2Visible}
              originalUserName={owner.username}
              canEditCab={canEditCab}
              itemName={itemName}
              ownerName={`${itemName}.owners.${ownerIndex}`}
              ownerIndex={ownerIndex}
              ownerLength={fields.length}
              removeOwner={removeOwner}
              totalDocumentRows={totalDocumentRows}
              openedPopoverId={openedPopoverId}
              setOpenedPopoverId={setOpenedPopoverId}
            />
          ))}
        </Table.Tbody>
      </Table>
      {isCoordinator && (
        <Box>
          <KanbanButton size='xs' variant='filled' leftSection={<IconPlus size={14} />} onClick={handleAddOwner} title='Add owner'>
            Add owner
          </KanbanButton>
        </Box>
      )}
    </Flex>
  );
});

export default TableEdit;
