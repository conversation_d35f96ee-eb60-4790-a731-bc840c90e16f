import { Edge, Node, useNodesState, useEdgesState } from '@xyflow/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Box, Card, Flex, Button, ScrollArea, Paper, Stack, Transition } from '@mantine/core';
import { KanbanIconButton, KanbanText, KanbanTitle } from 'kanban-design-system';
import { IconArrowLeft, IconX } from '@tabler/icons-react';
import useFetch from '@core/hooks/useFetch';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useNavigate, useParams } from 'react-router-dom';
import stylesCss from './WorkflowExecutionDetailPage.module.scss';
import { ChangeRequestWorkflowApi } from '@api/ChangeRequestWorkflowApi';
import WorkflowReactFlow from './reactFlow/WorkflowReactFlow';
import { WorkflowNodeStatusEnum } from '@core/schema/WorkflowNodeStatusEnum';
import NodeSettingsPanel from './NodeSettingsPanel';
import { ChangeRequestWorkflowNodeTypeEnum } from '@common/constants/ChangeWorkflowNodeType';
import { IconChevronRight } from '@tabler/icons-react';
import { buildChangeDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';

const formatTime = (time?: string | null) => (time ? new Date(time).toLocaleString() : '-');

const getDuration = (start?: string | null, end?: string | null) => {
  if (!start || !end) {
    return '-';
  }
  const durationMs = new Date(end).getTime() - new Date(start).getTime();
  if (isNaN(durationMs) || durationMs < 0) {
    return '-';
  }
  const seconds = Math.floor(durationMs / 1000) % 60;
  const minutes = Math.floor(durationMs / 60000) % 60;
  const hours = Math.floor(durationMs / 3600000);
  return `${hours}h ${minutes}m ${seconds}s`;
};

type WorkflowExecutionDetailPageProps = {
  id?: number;
};

type WorkflowJsonData = {
  nodes: Node[];
  edges: Edge[];
};

export const WorkflowExecutionDetailPage: React.FC<WorkflowExecutionDetailPageProps> = () => {
  const executionId = Number(useParams().id);
  const changeId = Number(useParams().changeId);
  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);
  const flowWrapperRef = useRef<HTMLDivElement>(null);
  const [selectedNode, setSelectedNode] = useState<Node | undefined>(undefined);
  // Sidebar open/close state
  const [sidebarOpened, setSidebarOpened] = useState(false);
  const navigate = useNavigate();
  // Fetch execution detail
  const { data: executionDetail } = useFetch(ChangeRequestWorkflowApi.findById(changeId, executionId), {
    enabled: !!executionId,
  });

  useEffect(() => {
    if (executionDetail?.data) {
      try {
        const workflow: WorkflowJsonData = JSON.parse(executionDetail?.data?.workflowData || '');
        setNodes(workflow.nodes);
        setEdges(workflow.edges);
      } catch (e) {
        // ignore
      }
    }
  }, [setEdges, setNodes, executionDetail?.data]);

  const handleSettingsClose = useCallback(() => {
    setSelectedNode(undefined);
  }, []);

  /**
   * Handle node click to show settings panel
   */
  const handleNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
    event.stopPropagation();

    // Skip sticky notes and start node
    if (node.type !== ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE) {
      return;
    }

    setSelectedNode(node);
  }, []);

  return (
    <Box className={stylesCss.pageContainer}>
      <HeaderTitleComponent
        title=''
        leftSection={
          <Flex direction='row' align='center' gap='xs'>
            <KanbanIconButton
              size='sm'
              variant='subtle'
              onClick={() =>
                navigate(
                  buildChangeDetailUrl(
                    ROUTE_PATH.CHANGE_REQUEST_DETAIL,
                    0,
                    changeId,
                    executionDetail?.data?.id ? EntityAction.EDIT : EntityAction.CREATE,
                    ChangeRquestTabsEnum.WORKFLOW,
                  ),
                )
              }>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz='h4'>
              {executionDetail?.data?.id ? `Execution: ${executionDetail?.data?.workflowName}  #${executionDetail?.data?.id}` : 'Execution Detail'}
            </KanbanTitle>
          </Flex>
        }
      />
      <Card withBorder shadow='sm' radius='md' className={stylesCss.workflowCard}>
        <Card.Section withBorder inheritPadding className={stylesCss.cardHeader}>
          <Flex align='center' gap='xs'>
            {/* Floating button to open sidebar */}
            <KanbanIconButton aria-label='Open sidebar' onClick={() => setSidebarOpened(true)} variant='light'>
              {/* Comment by: K-tool gencode */}
              <IconChevronRight size={22} />
            </KanbanIconButton>
          </Flex>
        </Card.Section>

        {/* Sidebar overlay - absolute, not affecting layout */}
        <Transition mounted={sidebarOpened} transition='slide-right' duration={300} timingFunction='ease'>
          {(transitionStyles) => (
            <Paper shadow='md' radius={12} className={stylesCss.sidebarPaper} style={transitionStyles}>
              {/* Header with close button */}
              <Flex className={stylesCss.sidebarHeader} justify='space-between' align='center'>
                <KanbanText size='lg' fw={700}>
                  Execution Details
                </KanbanText>
                <Button
                  onClick={() => setSidebarOpened(false)}
                  aria-label='Đóng sidebar'
                  variant='subtle'
                  size='sm'
                  className={stylesCss.sidebarCloseButton}>
                  <IconX size={18} />
                </Button>
              </Flex>
              {/* Scrollable content */}
              <ScrollArea.Autosize mah='100%' className={stylesCss.sidebarScrollArea}>
                <Stack gap='md'>
                  {/* Summary box */}
                  {executionDetail?.data && (
                    <Paper radius={8} className={stylesCss.summaryPaper}>
                      <Flex direction='column' gap={4}>
                        <Flex justify='space-between'>
                          <span className={stylesCss.statusStarted}>Started time:</span>
                          <span className={stylesCss.statusValue}>{formatTime(executionDetail.data.startTime)}</span>
                        </Flex>
                        <Flex justify='space-between'>
                          <span className={stylesCss.statusStarted}>Finished time:</span>
                          <span className={stylesCss.statusValue}>{formatTime(executionDetail.data.endTime)}</span>
                        </Flex>
                        <Flex justify='space-between'>
                          <span className={stylesCss.statusStarted}>Duration:</span>
                          <span className={stylesCss.statusValue}>{getDuration(executionDetail.data.startTime, executionDetail.data.endTime)}</span>
                        </Flex>
                        <Flex justify='space-between'>
                          <span style={{ color: '#6c7a89' }}>Status:</span>
                          <span
                            className={`${stylesCss.statusLabel} ${
                              executionDetail.data.status === WorkflowNodeStatusEnum.SUCCESS
                                ? stylesCss.statusSuccess
                                : executionDetail.data.status === WorkflowNodeStatusEnum.FAILURE
                                  ? stylesCss.statusFailure
                                  : executionDetail.data.status === WorkflowNodeStatusEnum.RUNNING
                                    ? stylesCss.statusRunning
                                    : stylesCss.statusOther
                            }`}>
                            {executionDetail.data.status}
                          </span>
                        </Flex>
                      </Flex>
                    </Paper>
                  )}
                  {/* Node executions list */}
                  {executionDetail?.data?.details && Array.isArray(executionDetail.data.details) && (
                    <Paper radius={8} className={stylesCss.nodeExecutionsPaper}>
                      <KanbanText size='md' fw={600} mb={8} className={stylesCss.nodeExecutionsTitle}>
                        Node Executions
                      </KanbanText>
                      <ul className={stylesCss.nodeExecutionsList}>
                        {executionDetail.data.details.map((node, index) => (
                          <li key={node.id || node.nodeId || index} className={stylesCss.nodeExecutionItem}>
                            <div className={stylesCss.nodeExecutionName}>{node.nodeName}</div>
                            <div className={stylesCss.nodeExecutionTime}>Start: {formatTime(node.startTime)}</div>
                            <div className={stylesCss.nodeExecutionTime}>End: {formatTime(node.endTime)}</div>
                            <div className={stylesCss.nodeExecutionDuration}>Duration: {getDuration(node.startTime, node.endTime)}</div>
                            <div className={stylesCss.nodeExecutionStatus}>
                              Status:{' '}
                              <span
                                className={`${stylesCss.nodeExecutionStatusValue} ${
                                  node.status === WorkflowNodeStatusEnum.SUCCESS
                                    ? stylesCss.nodeExecutionStatusSuccess
                                    : node.status === WorkflowNodeStatusEnum.FAILURE
                                      ? stylesCss.nodeExecutionStatusFailure
                                      : node.status === WorkflowNodeStatusEnum.RUNNING
                                        ? stylesCss.nodeExecutionStatusRunning
                                        : stylesCss.nodeExecutionStatusOther
                                }`}>
                                {node.status}
                              </span>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </Paper>
                  )}
                </Stack>
              </ScrollArea.Autosize>
            </Paper>
          )}
        </Transition>

        {selectedNode && (
          <NodeSettingsPanel
            changeId={changeId}
            workflowId={executionDetail?.data?.workflowId ?? 0}
            node={selectedNode}
            onClose={() => handleSettingsClose()}
          />
        )}

        {/* Diagram */}
        <Box ref={flowWrapperRef} className={stylesCss.flowWrapper}>
          <WorkflowReactFlow
            nodes={nodes}
            edges={edges}
            setEdges={setEdges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onNodeDoubleClick={handleNodeDoubleClick}
            setNodes={setNodes}
            isOnlyView={true}
          />
        </Box>
      </Card>
    </Box>
  );
};

export default WorkflowExecutionDetailPage;
