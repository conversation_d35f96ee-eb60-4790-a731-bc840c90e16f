<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="bf23aac7-e488-4b5f-b92d-a64bea006795" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/src/common/utils/UrlUtils.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/context/DocumentPermissionContext.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/hooks/useChangeRequestDocumentPermission.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/hooks/useGetCurrentUser.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/hooks/useItemRefs.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/themes/theme.module.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/CommentFormModal.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/App.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/App.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/ChangeRequestDocumentGroupApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/ChangeRequestDocumentGroupApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/common/constants/DateTimeConstants.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/common/constants/DateTimeConstants.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/common/constants/RegexConstant.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/common/constants/RegexConstant.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/common/constants/TextLengthConstants.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/common/constants/TextLengthConstants.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/GuardRoute.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/GuardRoute.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/appShell/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/appShell/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/hooks/useCheckPermissons.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/hooks/useCheckPermissons.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/hooks/useSavedColumns.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/hooks/useSavedColumns.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/schema/ChangeRequestRoleUser.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/schema/ChangeRequestRoleUser.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/themes/Default.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/themes/Default.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/models/ChangeRequestDocumentGroupModel.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/models/ChangeRequestDocumentGroupModel.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/changeList/ChangeNotePage.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/changeList/ChangeNotePage.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/ChangeRequestDetailPage.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/ChangeRequestDetailPage.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/components/CustomFieldRenderer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/components/CustomFieldRenderer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/detail/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/detail/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/DocumentGroupEditPage.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/DocumentGroupEditPage.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/DocumentGroupViewPage.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/DocumentGroupViewPage.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/AccordionControlEdit.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/AccordionControlEdit.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/AccordionPanelView.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/AccordionPanelView.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/DocumentAccordionView.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/DocumentAccordionView.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/TodoSidebar.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/TodoSidebar.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/selectBox/UserSelectComponent.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/selectBox/UserSelectComponent.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/selectBox/UserSingleSelectComponent.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/selectBox/UserSingleSelectComponent.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/table/CommonTable.module.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/table/CommonTable.module.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/table/LeaderCell.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/table/LeaderCell.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/table/OwnerRow.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/table/OwnerRow.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/table/TableEdit.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/table/TableEdit.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/components/table/UploaderFilePopover.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/components/table/UploaderFilePopover.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/utils/DocumentGroupMapper.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/utils/DocumentGroupMapper.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/document/utils/TableUtils.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/document/utils/TableUtils.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/ApprovalUserSelect.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/ApprovalUserSelect.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/ChangeRequestDetailRole.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/ChangeRequestDetailRole.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/CurrentUserCabTasks.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/CurrentUserCabTasks.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/NodeApproval.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/NodeApproval.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/NodeCabGroup.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/NodeCabGroup.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/NodeCabGroupList.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/NodeCabGroupList.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/NodeCabWorkflowTabs.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/NodeCabWorkflowTabs.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/RenderApproval.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/RenderApproval.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/RenderCAB.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/RenderCAB.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/approval/ChangeRequestApprovalComponent.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/approval/ChangeRequestApprovalComponent.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/role/notify/ApprovalNotificationForm.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/role/notify/ApprovalNotificationForm.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/workflow/NodeSettingsPanel.module.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/workflow/NodeSettingsPanel.module.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/workflow/NodeSettingsPanel.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/workflow/NodeSettingsPanel.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/request/workflow/WorkflowRightSideBar.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/request/workflow/WorkflowRightSideBar.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/template/components/FieldRenderer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/template/components/FieldRenderer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/configuration/notifications/emailTemplate/EmailTemplateDetailPage.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/configuration/notifications/emailTemplate/EmailTemplateDetailPage.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/sideBar/SideBarSelection.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/sideBar/SideBarSelection.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="31lLbRgHr5hhI2UmdBMQQJxYk4k" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "Merging change-review"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bf23aac7-e488-4b5f-b92d-a64bea006795" name="Changes" comment="" />
      <created>1756093784936</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756093784936</updated>
      <workItem from="1756093789038" duration="512000" />
    </task>
    <servers />
  </component>
</project>