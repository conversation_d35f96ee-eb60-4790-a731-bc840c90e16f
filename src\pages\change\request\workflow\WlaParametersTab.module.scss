.variablesContainer {
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  padding: 16px;
  margin-top: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--mantine-color-gray-2);
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: var(--mantine-color-gray-8);
  margin: 0;
}

.tableHeader {
  display: grid;
  grid-template-columns: 1fr 1fr 80px;
  gap: 12px;
  padding: 8px 12px;
  background: var(--mantine-color-gray-0);
  border-radius: var(--mantine-radius-sm);
  font-size: 13px;
  font-weight: 500;
  color: var(--mantine-color-gray-6);
  margin-bottom: 8px;
}

.scrollArea {
  margin-bottom: 12px;
}

.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  color: var(--mantine-color-gray-5);
  font-size: 14px;
  font-style: italic;
}

.variablesList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variableRow {
  display: grid;
  grid-template-columns: 1fr 1fr 80px;
  gap: 12px;
  padding: 8px 12px;
  background: var(--mantine-color-gray-0);
  border-radius: var(--mantine-radius-sm);
  transition: background-color 0.2s;

  &:hover {
    background: var(--mantine-color-gray-1);
  }
}

.colKey,
.colValue {
  display: flex;
  align-items: center;
}

.colAction {
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 12px;
  border-top: 1px solid var(--mantine-color-gray-2);
}