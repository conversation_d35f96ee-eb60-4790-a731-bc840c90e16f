import { Edge, Node, useNodesState, useEdgesState, OnConnect } from '@xyflow/react';
import { v4 as uuidv4 } from 'uuid';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { WorkflowRightSideBar } from './WorkflowRightSideBar';
import { Box, Card, Flex, Modal, Group } from '@mantine/core';
import { KanbanButton, KanbanIconButton, KanbanInput, KanbanText, KanbanTitle } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { ChangeWorkflowNode, ChangeWorkflowTypeEnum } from '@core/schema/ChangeWorkflowNode';
import { CustomNodeData } from '@pages/change/request/workflow/reactFlow/CustomNode';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Icon<PERSON>rrowLeft, IconEdit, IconDeviceFloppy } from '@tabler/icons-react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import stylesCss from './WorkflowDetailPage.module.scss';
import useMutate from '@core/hooks/useMutate';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { ChangeRequestWorkflowNodeTypeEnum } from '@common/constants/ChangeWorkflowNodeType';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { buildChangeDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { changePermissionConfigs } from '@pages/change/changeList';
import WorkflowReactFlow from './reactFlow/WorkflowReactFlow';
import jenkins from 'access/icons/Jenkins.svg';
import wla from 'access/icons/wla.svg';
import flowChildren from 'access/icons/flow-children.svg';
import playerPlay from 'access/icons/player-play.svg';
import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';
import { ChangeRequestWorkflowApi } from '@api/ChangeRequestWorkflowApi';
import { EdgeAction } from './reactFlow/AnimatedEdge';
import LogViewer from './LogViewer';
import { JenkinsStageStatusEnum, WebsocketDataTypeEnum, WlaStatusEnum } from '@core/schema/WebsocketData';
import { WorkflowExecutionTypeEnum } from '@core/schema/ChangeRequestWorkflowExecute';
import useRegisterWebsocketListener from '@core/hooks/useRegisterWebsocketListener';
type WorkFlowNodeDetailPageProps = {
  id?: number;
};

export type ExtendedNode = Node & {
  changeNodeId?: number;
};

type WorkflowJsonData = {
  nodes: ExtendedNode[];
  edges: Edge[];
};

export type WorkflowData = {
  id?: number;
  name: string;
  type: ChangeWorkflowTypeEnum;
  description?: string;
  workflowData: string;
  nodes: ExtendedNode[];
  edges: Edge[];
  changeId: number;
  createdDate?: string;
  modifiedDate?: string;
  isUpdateNode: boolean;
};

export const getNodeIcon = (node: ChangeWorkflowNode) => {
  if (ChangeRequestWorkflowNodeTypeEnum.CHECKLIST_NODE === node.type) {
    return flowChildren;
  }
  switch (node.application) {
    case ChangeApplicationTypeEnum.JENKINS:
      return jenkins;
    case ChangeApplicationTypeEnum.WLA:
      return wla;
    default:
      return playerPlay;
  }
};

const generateWorkflowName = () => {
  const now = new Date();
  const dateStr = now.toLocaleDateString('vi-VN');
  const timeStr = now.toLocaleTimeString('vi-VN', { hour12: false });

  return `Workflow ${dateStr} ${timeStr}`;
};

export const WorkflowDetailPage: React.FC<WorkFlowNodeDetailPageProps> = () => {
  const id = Number(useParams().id);
  const changeId = Number(useParams().changeId);
  const navigate = useNavigate();
  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);
  // State cho node đang xem log (độc lập với selectedNode của sidebar)
  const [selectedNode, setSelectedNode] = useState<Node | undefined>(undefined);
  const flowWrapperRef = useRef<HTMLDivElement>(null);
  const [listNodeOpened, setlistNodeOpened] = useState(false);
  const canEditChange = useCheckPermissons([changePermissionConfigs.edit]);
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const isViewMode = actionParam === EntityAction.VIEW;
  const [nodeLogId, setNodeLogId] = useState<string | undefined>(undefined);
  const [nodeLog, setNodeLog] = useState<Node | undefined>(undefined);

  const { mutate: saveWorkflowMutate } = useMutate(ChangeRequestApi.saveWorkflow, {
    successNotification: 'Workflow saved successfully',
    onSuccess: () => {
      navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, 0, changeId, EntityAction.VIEW, ChangeRquestTabsEnum.WORKFLOW));
    },
  });

  // run workflow
  const { mutate: runWorkflowMutate } = useMutate(ChangeRequestWorkflowApi.execution, {
    successNotification: 'Run workflow successfully',
  });

  const { data: workflowDetail } = useFetch(ChangeRequestApi.findDetailWorkflow(changeId, id), {
    enabled: !!id,
  });

  // Thêm vào WorkflowDetailPage component
  const hasNodeErrors = useMemo(() => {
    return nodes.some((node) => {
      // Kiểm tra lỗi trong node data
      const nodeData = node.data;

      // Kiểm tra isError flag
      if (nodeData?.errors) {
        return true;
      }
      return false;
    });
  }, [nodes]);

  // Kiểm tra workflow có sẵn sàng để run không
  const isWorkflowReady = useMemo(() => {
    // Phải có ít nhất 1 node (ngoài start node)
    const executableNodes = nodes.filter(
      (node) => node.type === ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE || node.type === ChangeRequestWorkflowNodeTypeEnum.CHECKLIST_NODE,
    );

    if (executableNodes.length === 0) {
      return false;
    }

    // Không được có node nào bị lỗi
    if (hasNodeErrors) {
      return false;
    }

    // Kiểm tra có edges kết nối không (optional)
    const hasConnections = edges.length > 0;

    return hasConnections;
  }, [nodes, edges, hasNodeErrors]);

  useEffect(() => {
    const nodeLog = nodes.find((node) => node.id === nodeLogId);
    setNodeLog(nodeLog);
  }, [nodeLogId, nodes]);

  useRegisterWebsocketListener((message) => {
    if (message.type === WebsocketDataTypeEnum.JENKIN_STAGE) {
      const stageData = message.content;

      // Phân loại các stage theo trạng thái
      const hasRunningStage = stageData.datas.some((stage) => stage.status === JenkinsStageStatusEnum.IN_PROGRESS);

      setNodes((prevNodes) =>
        prevNodes.map((node) => {
          if (node.id === stageData.nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                isRunning: hasRunningStage,
              },
            };
          }
          return node;
        }),
      );
    }

    if (message.type === WebsocketDataTypeEnum.WLA_INSTANCE) {
      const messages = message.content;
      const hasRunningStage = messages.instances.some((instance) => instance.status === WlaStatusEnum.RUNNING);

      setNodes((prevNodes) =>
        prevNodes.map((node) => {
          if (node.id === messages.nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                isRunning: hasRunningStage,
              },
            };
          }
          return node;
        }),
      );
    }
  });

  useEffect(() => {
    if (workflowDetail?.data) {
      setWorkflowData((prev) => ({
        ...prev,
        name: workflowDetail?.data?.name || '',
        type: workflowDetail?.data?.type || ChangeWorkflowTypeEnum.CHECKLIST,
      }));
      try {
        const workflow: WorkflowJsonData = JSON.parse(workflowDetail?.data?.workflowData || '');
        setNodes(workflow.nodes);
        setEdges(workflow.edges);
      } catch (e) {
        /* empty */
      }
    }
  }, [setEdges, setNodes, workflowDetail?.data]);

  // Workflow data state
  const [workflowData, setWorkflowData] = useState<WorkflowData>({
    id: id,
    name: generateWorkflowName(),
    type: ChangeWorkflowTypeEnum.CHECKLIST,
    description: '',
    changeId: changeId,
    workflowData: '{}',
    nodes: [],
    edges: [],
    isUpdateNode: true,
  });

  const [isEditNameModalOpen, setIsEditNameModalOpen] = useState(false);
  const [editingName, setEditingName] = useState('');

  /**
   * Handle open edit name modal
   */
  const handleOpenEditNameModal = useCallback(() => {
    setEditingName(workflowData.name);
    setIsEditNameModalOpen(true);
  }, [workflowData.name]);

  /**
   * Handle close edit name modal
   */
  const handleCloseEditNameModal = useCallback(() => {
    setIsEditNameModalOpen(false);
    setEditingName('');
  }, []);

  /**
   * Handle save workflow name
   */
  const handleSaveWorkflowName = useCallback(() => {
    if (editingName.trim()) {
      setWorkflowData((prev) => ({
        ...prev,
        name: editingName.trim(),
        modifiedDate: new Date().toISOString(),
      }));
      setIsEditNameModalOpen(false);
      notifications.show({
        title: 'Success',
        message: 'Workflow name updated successfully',
        color: 'green',
      });
    }
  }, [editingName]);

  /**
   * Handle onConnect
   */
  const handleOnConnect = useCallback<OnConnect>(
    (connection) => {
      if (!connection.source || !connection.target) {
        return;
      }

      const newEdges: Edge[] = [];

      // Helper function để lấy parentId của node
      const getParentId = (nodeId: string): string | undefined => {
        const node = nodes.find((n) => n.id === nodeId);
        return node?.parentId;
      };

      // Helper function để kiểm tra edge đã tồn tại
      const edgeExists = (source: string, target: string): boolean => {
        return edges.some((edge) => (edge.source === source && edge.target === target) || (edge.source === target && edge.target === source));
      };

      // Lấy parentId của source và target
      const sourceParentId = getParentId(connection.source);
      const targetParentId = getParentId(connection.target);

      // Case 1: Node ngoài kết nối vào node trong group
      // Source không có parent, target có parent
      if (!sourceParentId && targetParentId) {
        // Tạo edge từ source node đến parent của target
        if (!edgeExists(connection.source, targetParentId)) {
          const parentEdge: Edge = {
            id: `edge-${connection.source}-${targetParentId}-${Date.now()}-parent`,
            source: connection.source,
            target: targetParentId,
            type: 'dashedEdge',
            hidden: true,
            animated: false,
            data: {
              isAutoGenerated: true, // Đánh dấu đây là edge tự động
              childConnection: {
                source: connection.source,
                target: connection.target,
              },
              action: 'default',
            },
          };
          newEdges.push(parentEdge);
        }
      }

      // Case 2: Node trong group kết nối ra node ngoài
      // Source có parent, target không có parent
      else if (sourceParentId && !targetParentId) {
        // Tạo edge từ parent của source đến target node
        if (!edgeExists(sourceParentId, connection.target)) {
          const parentEdge: Edge = {
            id: `edge-${sourceParentId}-${connection.target}-${Date.now()}-parent`,
            source: sourceParentId,
            target: connection.target,
            hidden: true,
            type: 'dashedEdge',
            animated: false,
            data: {
              isAutoGenerated: true,
              childConnection: {
                source: connection.source,
                target: connection.target,
              },
              action: 'default',
            },
          };
          newEdges.push(parentEdge);
        }
      }

      // Case 3: Kết nối giữa 2 node trong 2 group khác nhau
      // Cả source và target đều có parent và parent khác nhau
      else if (sourceParentId && targetParentId && sourceParentId !== targetParentId) {
        // Tạo edge giữa 2 parent nodes
        if (!edgeExists(sourceParentId, targetParentId)) {
          const parentEdge: Edge = {
            id: `edge-${sourceParentId}-${targetParentId}-${Date.now()}-parent`,
            source: sourceParentId,
            target: targetParentId,
            type: 'dashedEdge',
            hidden: true,
            animated: false,
            data: {
              isAutoGenerated: true,
              childConnection: {
                source: connection.source,
                target: connection.target,
              },
              action: 'default',
            },
          };
          newEdges.push(parentEdge);
        }
      }

      // Case 4: Kết nối giữa 2 node trong cùng group
      // Cả source và target có cùng parentId -> không cần tạo parent edge

      // Thêm tất cả edges mới vào danh sách edges
      setEdges((currentEdges) => [...currentEdges, ...newEdges]);

      // Nếu cần trigger các action khác (như save state cho undo/redo)
      // có thể thêm ở đây
    },
    [nodes, edges, setEdges], // dependencies
  );

  /**
   * Handle node click to show settings panel
   */
  const handleNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
    event.stopPropagation();

    // Skip sticky notes and start node
    if (node.type !== ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE) {
      return;
    }

    setSelectedNode(node);
  }, []);

  /**
   * Handle edge type update (TRUE/FALSE/DEFAULT)
   */
  const handleEdgeTypeUpdate = useCallback(
    (edgeId: string, type: string) => {
      setEdges((prevEdges) =>
        prevEdges.map((edge) => {
          if (edge.id === edgeId) {
            return {
              ...edge,
              data: {
                ...edge.data,
                action: type ? type : EdgeAction.SUCCESS,
              },
            };
          }
          return edge;
        }),
      );
    },
    [setEdges],
  );

  /**
   * method create node
   */
  const handleNodeCreate = useCallback(
    (nodeData: ChangeWorkflowNode) => {
      if (!flowWrapperRef.current) {
        return;
      }

      // Calculate position for the new node
      const position = {
        x: Math.random() * 300 + 100,
        y: Math.random() * 200 + 100,
      };
      const nodeId: string = nodeData.nodeId ? nodeData.nodeId : `${nodeData.nodeId}_${Math.random()}`;

      const newNode: Node = {
        id: nodeId,
        type: nodeData.type,
        position,
        data: {
          changeNodeId: nodeData.id,
          label: `${nodeData.nodeName}`,
          subtitle: ``,
          icon: getNodeIcon(nodeData),
          parameters: {},
          settings: {},
          application: nodeData.application,
        },
      };

      setNodes((prev) => [...prev, newNode]);
      // If a node is currently selected, connect the new node to the selected node
      const nodeSelectedInDiragram: Node | undefined = nodes.find((node) => node.selected);

      // Auto create edges when node
      if (
        nodeData.type === ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE &&
        nodeSelectedInDiragram &&
        `${nodeSelectedInDiragram.type}` === `${ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE}` &&
        !!nodeSelectedInDiragram.hidden
      ) {
        const connectionEdge: Edge = {
          id: `edge-${nodeSelectedInDiragram.id}-${nodeData.nodeId}`,
          source: `${nodeSelectedInDiragram.id}`,
          target: nodeId,
          type: 'animated',
          animated: false,
          data: {
            onDelete: (edgeId: string) => {
              setEdges((eds) => eds.filter((e) => e.id !== edgeId));
            },
            onTypeUpdate: handleEdgeTypeUpdate,
            action: EdgeAction.SUCCESS,
          },
        };

        setEdges((prev) => [...prev, connectionEdge]);
      }

      // build child node
      if (nodeData.childrenData && nodeData.type === ChangeRequestWorkflowNodeTypeEnum.CHECKLIST_NODE) {
        try {
          const workflow: WorkflowJsonData = JSON.parse(nodeData.childrenData || '');
          let childNodes: Node[] = workflow.nodes;
          const childEdges: Edge[] = workflow.edges;

          // Map old node ids to new unique ids
          const idMap: Record<string, string> = {};
          childNodes.forEach((child) => {
            idMap[child.id] = uuidv4();
          });

          // Update child nodes with new ids and parentId
          childNodes = childNodes.map((child) => ({
            ...child,
            id: idMap[child.id],
            parentId: newNode.id,
            extent: 'parent',
            expandParent: true,
          }));

          // Update edges to use new node ids and generate new edge ids
          const updatedChildEdges = childEdges.map((edge) => ({
            ...edge,
            id: uuidv4(),
            source: idMap[edge.source] || edge.source,
            target: idMap[edge.target] || edge.target,
          }));

          setNodes((prev) => [...prev, ...childNodes]);
          setEdges((prev) => [...prev, ...updatedChildEdges]);
        } catch (e) {
          /* empty */
        }
      }

      // Show settings panel for the new node
      if (newNode.type === ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE) {
        setSelectedNode(newNode);
      }
    },
    [nodes, setNodes, handleEdgeTypeUpdate, setEdges],
  );

  /**
   * save param and setting of node
   */
  const handleNodeSave = useCallback(
    (nodeId: string, data?: CustomNodeData) => {
      setNodes((prev) =>
        prev.map((node) => {
          if (node.id === nodeId) {
            const nodeChange = {
              ...node,
              data: {
                ...node.data,
                parameters: data?.parameters,
                settings: data?.settings,
              },
            };
            return nodeChange;
          }
          return node;
        }),
      );

      // Reset selected node
      setSelectedNode(undefined);
    },
    [setNodes],
  );

  const handleSettingsClose = useCallback(() => {
    setSelectedNode(undefined);
  }, []);

  /**
   * Save workflow to backend
   */
  const handleSaveWorkflow = useCallback(() => {
    const workflowToSave: WorkflowData = {
      ...workflowData,
      nodes: nodes,
      edges: edges,
      workflowData: JSON.stringify({
        nodes: nodes,
        edges: edges,
      }),
    };

    saveWorkflowMutate(workflowToSave);
  }, [workflowData, nodes, edges, saveWorkflowMutate]);

  return (
    <Box className={stylesCss.pageContainer}>
      {/* Edit Name Modal */}
      <Modal opened={isEditNameModalOpen} onClose={handleCloseEditNameModal} title='Edit Workflow Name' size='md'>
        <KanbanInput
          label='Workflow Name'
          placeholder='Enter workflow name'
          value={editingName}
          onChange={(event) => setEditingName(event.currentTarget.value)}
          data-autofocus
          mb='md'
          maxLength={COMMON_MAX_LENGTH}
        />
        <Group justify='flex-end'>
          <KanbanButton variant='outline' onClick={handleCloseEditNameModal}>
            Cancel
          </KanbanButton>
          <KanbanButton onClick={handleSaveWorkflowName} disabled={!editingName.trim() || !canEditChange || isViewMode}>
            Save
          </KanbanButton>
        </Group>
      </Modal>

      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex direction='row' align='center' gap='xs'>
            <KanbanButton
              size='xs'
              variant='light'
              onClick={() =>
                navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, 0, changeId, EntityAction.EDIT, ChangeRquestTabsEnum.WORKFLOW))
              }>
              Cancel
            </KanbanButton>
            {isViewMode && canEditChange && (
              <>
                <KanbanButton
                  size='xs'
                  onClick={() => {
                    navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL, changeId, id || 0, EntityAction.EDIT));
                  }}>
                  Edit
                </KanbanButton>
                <KanbanButton
                  size='xs'
                  disabled={!canEditChange || !isWorkflowReady}
                  onClick={() => {
                    runWorkflowMutate({ changeId: changeId, workflowId: id, type: WorkflowExecutionTypeEnum.MANUAL });
                  }}
                  leftSection={<IconDeviceFloppy size={14} />}>
                  Run
                </KanbanButton>
              </>
            )}
            {!isViewMode && (
              <KanbanButton size='xs' disabled={!canEditChange} onClick={handleSaveWorkflow} leftSection={<IconDeviceFloppy size={14} />}>
                Save
              </KanbanButton>
            )}
          </Flex>
        }
        leftSection={
          <Flex direction='row' align='center' gap='xs'>
            <KanbanIconButton
              size='sm'
              variant='subtle'
              onClick={() =>
                navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, 0, changeId, EntityAction.EDIT, ChangeRquestTabsEnum.WORKFLOW))
              }>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz='h4'>
              {workflowDetail?.data?.id ? `${isViewMode ? '' : 'Edit'} ${workflowDetail?.data?.name}` : 'Create new Workflow'}
            </KanbanTitle>
          </Flex>
        }
      />
      <Card withBorder shadow='sm' radius='md' className={stylesCss.workflowCard}>
        <Card.Section withBorder inheritPadding className={stylesCss.cardHeader}>
          <Flex align='center' gap='xs'>
            <KanbanText fw={500} size='lg'>
              {workflowData.name}
            </KanbanText>
            {canEditChange && !isViewMode && (
              <KanbanIconButton size='xs' variant='subtle' onClick={handleOpenEditNameModal} title='Edit workflow name'>
                <IconEdit size={14} />
              </KanbanIconButton>
            )}
          </Flex>
        </Card.Section>

        <WorkflowRightSideBar
          changeId={changeId}
          workflowId={id}
          onNodeCreate={handleNodeCreate}
          selectedNode={selectedNode}
          onNodeSave={handleNodeSave}
          onSettingsClose={handleSettingsClose}
          listNodeOpened={listNodeOpened}
          setListNodeOpened={setlistNodeOpened}
          nodes={nodes}
          type={workflowDetail?.data?.type}
        />
        <div ref={flowWrapperRef} className={stylesCss.flowWrapper}>
          <WorkflowReactFlow
            onConnect={handleOnConnect}
            nodes={nodes}
            edges={edges}
            setEdges={setEdges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onNodeDoubleClick={handleNodeDoubleClick}
            onNodeClick={(_event, node) => {
              setSelectedNode(node);
            }}
            onEdgeTypeUpdate={handleEdgeTypeUpdate}
            setNodes={setNodes}
            isOnlyView={isViewMode}
            onLogClick={(nodeId) => {
              setNodeLogId(nodeId);
            }}
          />
        </div>
        {nodeLog && (
          <Box mt='md'>
            <LogViewer
              node={nodeLog}
              onClose={() => {
                setNodeLogId(undefined);
              }}
            />
          </Box>
        )}
      </Card>
    </Box>
  );
};

export default WorkflowDetailPage;
