import React, { useCallback, useState } from 'react';
import { Draggable, Droppable } from '@hello-pangea/dnd';
import { Control, Controller, useFieldArray, useFormContext } from 'react-hook-form';

import { ActionIcon, Box, Group, Paper } from '@mantine/core';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { IconTrash } from '@tabler/icons-react';
import { KanbanIconButton, KanbanInput } from 'kanban-design-system';
import styled from '../ChangeRequestDetail.module.scss';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { IconEdit } from '@tabler/icons-react';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { ChangeRequestApprovalComponent } from './approval/ChangeRequestApprovalComponent';
import { MappedUser } from '@pages/change/request/role/ChangeRequestDetailRole';

interface ApprovalCabGroupComponentProps {
  roleIdx: number;
  workflowIdx: number;
  cabGroupIdx: number;
  changeFlowNodeId: number;
  control: Control<ChangeRequestRoleList>;
  onDeleteCabGroup: () => void;
  mode: EntityAction;
  handleCheckboxChange: (user: MappedUser, checked: boolean) => void;
  modeApproval: ChangeRequestApprovalMode;
  refetchChangeRequestRoles: () => void;
  onRefetchAll?: () => void;
}
const NodeCabGroup: React.FC<ApprovalCabGroupComponentProps> = ({
  cabGroupIdx,
  changeFlowNodeId,
  control,
  handleCheckboxChange,
  mode,
  modeApproval,
  onDeleteCabGroup,
  onRefetchAll,
  refetchChangeRequestRoles,
  roleIdx,
  workflowIdx,
}) => {
  const groupPath = `roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}` as const;
  const usersPath = `${groupPath}.users` as const;
  const { fields: cabGroupUsers, remove: removeUser } = useFieldArray({
    control,
    name: usersPath,
  });

  const {
    formState: { errors },
  } = useFormContext<ChangeRequestRoleList>();
  const groupNameError = errors?.roles?.[roleIdx]?.workflows?.[workflowIdx]?.groups?.[cabGroupIdx]?.cabGroupName;
  const [enableEditGroupName, setEnableEditGroupName] = useState(false);

  const handleRemoveUser = useCallback(
    (cabGroupUserIdx: number) => {
      removeUser(cabGroupUserIdx);

      if (cabGroupUsers.length === 1) {
        onDeleteCabGroup();
      }
    },
    [cabGroupUsers.length, onDeleteCabGroup, removeUser],
  );
  const isViewMode = EntityAction.VIEW === mode;

  return (
    <Paper shadow='sm' p='xs' withBorder radius='md'>
      <Group justify='space-between' px='md'>
        <Group w={'50%'}>
          {!isViewMode && !enableEditGroupName && (
            <KanbanIconButton variant='subtle' size='xs' onClick={() => setEnableEditGroupName(true)}>
              <IconEdit />
            </KanbanIconButton>
          )}
          <Controller
            control={control}
            name={`roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}.cabGroupName`}
            render={({ field }) => (
              <KanbanInput
                value={field.value ?? `Group ${cabGroupIdx + 1}`}
                onChange={(e) => (enableEditGroupName ? field.onChange(e.target.value) : undefined)}
                maw={'50%'}
                disabled={isViewMode}
                readOnly={!enableEditGroupName}
                styles={{
                  input: {
                    border: enableEditGroupName ? '' : 'none',
                    borderRadius: enableEditGroupName ? '' : 'unset',
                    paddingLeft: !enableEditGroupName ? 0 : undefined,
                  },
                }}
                onBlur={() => {
                  field.onBlur();
                  setEnableEditGroupName(false);
                }}
                error={groupNameError ? groupNameError.message : ''}
                maxLength={COMMON_MAX_LENGTH}
                mb={0}
                withAsterisk
                fw={500}
              />
            )}
          />
        </Group>

        {!isViewMode && (
          <ActionIcon variant='subtle' color='red' onClick={onDeleteCabGroup} title='Delete Group'>
            <IconTrash size={18} />
          </ActionIcon>
        )}
      </Group>
      <Droppable droppableId={cabGroupIdx.toString()}>
        {(provided, snapshot) => (
          <Box
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`${styled.dropWrapper} ${snapshot.isDraggingOver ? styled.isDraggingOver : ''}`}>
            {cabGroupUsers.length === 0 ? (
              <Box style={{ color: '#adb5bd', textAlign: 'center', padding: 16 }}>Drag user here</Box>
            ) : (
              cabGroupUsers.map((cabGroupUser, cabGroupUserIdx) => (
                <Draggable key={cabGroupUser.id} draggableId={`roleUser-${cabGroupUser.id}`} index={cabGroupUserIdx} isDragDisabled={isViewMode}>
                  {(provided, snapshot) => (
                    <Box
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      ref={provided.innerRef}
                      style={{
                        ...provided.draggableProps.style,
                      }}
                      className={snapshot.isDragging ? styled.dragItem : ''}>
                      <ChangeRequestApprovalComponent
                        mode={mode}
                        workflowIdx={workflowIdx}
                        control={control}
                        cabGroupIdx={cabGroupIdx}
                        cabGroupUserIdx={cabGroupUserIdx}
                        onDelete={() => handleRemoveUser(cabGroupUserIdx)}
                        roleIdx={roleIdx}
                        changeFlowNodeId={changeFlowNodeId || 0}
                        modeApproval={modeApproval}
                        handleCheckboxChange={handleCheckboxChange}
                        refetchChangeRequestRoles={refetchChangeRequestRoles}
                        onRefetchAll={onRefetchAll}
                      />
                    </Box>
                  )}
                </Draggable>
              ))
            )}
            {provided.placeholder}
          </Box>
        )}
      </Droppable>
    </Paper>
  );
};
export default NodeCabGroup;
